# Devo Table Discovery System

A comprehensive table discovery and inventory system for Devo environments. This tool scans and discovers all tables that exist in your Devo instance, providing detailed information about each table including schemas, record counts, and metadata.

## Features

- **Comprehensive Table Discovery**: Automatically discovers all available tables in your Devo environment
- **Multiple Discovery Methods**: Uses system queries, pattern scanning, and prefix enumeration
- **Detailed Table Analysis**: Provides schema information, record counts, and last updated timestamps
- **Parallel Processing**: Uses threading for efficient analysis of multiple tables
- **Progress Indicators**: Shows real-time progress during the discovery process
- **JSON Export**: Saves results in structured JSON format with timestamps
- **Error Handling**: Robust error handling for API rate limits and connection issues
- **Verbose Logging**: Detailed logging for troubleshooting and monitoring

## Installation

The table discovery system uses the same dependencies as the main backup system:

```bash
pip install -r requirements.txt
```

Required dependencies:
- `devo-sdk` - Official Devo SDK for API access
- `python-dotenv` - Environment variable management
- Standard Python libraries (json, threading, argparse, etc.)

## Configuration

Uses the same configuration as the main backup system. Ensure your `.env` file contains:

```env
DEVO_API_KEY=your_api_key_here
DEVO_API_SECRET=your_api_secret_here
DEVO_QUERY_ENDPOINT=https://api-apac.devo.com/search/query
```

Optional configuration:
```env
API_TIMEOUT=300          # Request timeout in seconds
MAX_RETRIES=3           # Maximum retry attempts
RETRY_DELAY=5.0         # Delay between retries in seconds
RATE_LIMIT_DELAY=1.0    # Delay between requests in seconds
```

## Usage

### Basic Discovery

Discover all tables with default settings:
```bash
python table_discovery.py
```

### Custom Output File

Specify a custom output filename:
```bash
python table_discovery.py --output my_tables.json
```

### Verbose Mode

Enable detailed logging for troubleshooting:
```bash
python table_discovery.py --verbose
```

### Help

View all available options:
```bash
python table_discovery.py --help
```

## Discovery Methods

The system uses multiple methods to ensure comprehensive table discovery:

### 1. System Metadata Queries
Attempts to query Devo system tables for table metadata:
- `from siem.logtrust.table.info select table`
- `from system.table.metadata select table_name`
- `from devo.system.tables select name`

### 2. Pattern Scanning
Tests common Devo table patterns:
- SIEM tables: `siem.logtrust.*`
- Firewall tables: `firewall.*`
- Network tables: `network.*`
- Application tables: `app.*`
- System tables: `system.*`
- Cloud tables: `cloud.*`

### 3. Prefix Enumeration
Scans known table prefixes to discover additional tables.

## Output Format

Results are saved in JSON format with the following structure:

```json
{
  "discovery_metadata": {
    "timestamp": "2025-07-03T13:19:23.721802",
    "discovery_duration_seconds": 16.45,
    "devo_endpoint": "https://api-apac.devo.com/search/query",
    "discovery_version": "1.0.0"
  },
  "statistics": {
    "total_tables_found": 28,
    "successful_discoveries": 26,
    "failed_discoveries": 2,
    "total_estimated_records": 1250000,
    "discovery_errors": []
  },
  "tables": {
    "table_name": {
      "table_name": "siem.logtrust.web.activity",
      "success": true,
      "schema": {
        "fields": ["timestamp", "user", "action", "ip"],
        "field_types": {
          "timestamp": "string",
          "user": "string",
          "action": "string",
          "ip": "string"
        },
        "inferred": true
      },
      "estimated_records": 50000,
      "last_updated": "2025-07-03T12:45:30",
      "size_info": {},
      "sample_data": [
        {"timestamp": "2025-07-03T12:45:30", "user": "admin", "action": "login", "ip": "***********"}
      ],
      "error": null,
      "analysis_timestamp": "2025-07-03T13:19:15.023757",
      "count_period": "current_day"
    }
  }
}
```

## Table Information

For each discovered table, the system provides:

- **Table Name**: Full table identifier
- **Success Status**: Whether analysis was successful
- **Schema Information**: 
  - Field names
  - Inferred field types
  - Schema discovery method
- **Record Count**: Estimated number of records (current day)
- **Last Updated**: Most recent record timestamp (if available)
- **Sample Data**: Up to 3 sample records
- **Error Information**: Any errors encountered during analysis

## Testing

Test the table discovery system:

```bash
python test_table_discovery.py
```

This will run comprehensive tests including:
- Connection testing
- Table discovery methods
- Single table analysis
- Results generation and saving
- Pattern scanning functionality
- System query testing

## Integration with Backup System

The table discovery system integrates with the main backup system. You can use discovered tables to:

1. **Update Available Tables**: Replace the hardcoded table list in `devo_client.py`
2. **Selective Backup**: Choose specific tables for backup based on discovery results
3. **Backup Planning**: Use record counts and sizes for backup planning

### Updating the Backup System

To use discovered tables in your backup system:

1. Run table discovery:
   ```bash
   python table_discovery.py --output discovered_tables.json
   ```

2. Extract table names from results:
   ```python
   import json
   
   with open('results/discovered_tables.json', 'r') as f:
       results = json.load(f)
   
   # Get successful tables with data
   active_tables = [
       name for name, info in results['tables'].items()
       if info['success'] and info['estimated_records'] > 0
   ]
   ```

3. Update `devo_client.py`:
   ```python
   def get_available_tables(self) -> List[str]:
       return active_tables  # Use discovered tables
   ```

## Troubleshooting

### Common Issues

1. **No Tables Discovered**:
   - Check API credentials and endpoint
   - Verify network connectivity
   - Try verbose mode for detailed logging

2. **All Tables Show 0 Records**:
   - Tables might be empty for the current day
   - Try querying a different date range
   - Check if tables contain historical data only

3. **Connection Timeouts**:
   - Increase `API_TIMEOUT` in configuration
   - Check network stability
   - Reduce concurrent analysis threads

4. **Rate Limiting**:
   - Increase `RATE_LIMIT_DELAY` in configuration
   - The system automatically handles rate limits
   - Consider running during off-peak hours

### Verbose Logging

Use `--verbose` flag for detailed logging:
```bash
python table_discovery.py --verbose
```

This provides:
- Detailed query execution logs
- Table analysis progress
- Error details and stack traces
- Performance timing information

## Performance Considerations

- **Parallel Processing**: Uses up to 5 concurrent threads for table analysis
- **Rate Limiting**: Respects API rate limits with configurable delays
- **Memory Usage**: Limits sample data to prevent memory issues
- **Query Optimization**: Uses efficient queries for counting and schema discovery

## File Structure

```
├── table_discovery.py          # Main discovery script
├── test_table_discovery.py     # Test suite
├── TABLE_DISCOVERY_README.md   # This documentation
└── results/                    # Discovery results directory
    └── table_discovery_results_YYYYMMDD_HHMMSS.json
```

## Contributing

When adding new discovery methods or improving existing functionality:

1. Follow the existing code structure and error handling patterns
2. Add appropriate logging and progress indicators
3. Update tests in `test_table_discovery.py`
4. Update this documentation

## License

This table discovery system is part of the Devo API Backup System and follows the same licensing terms.
