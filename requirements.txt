# Devo API Data Backup System Dependencies

# Core dependencies
requests>=2.31.0
python-dotenv>=1.0.0
urllib3>=2.0.0

# Optional dependencies for enhanced functionality
# Uncomment if you need these features:

# For advanced JSON processing
# ujson>=5.8.0

# For better HTTP/2 support
# httpx>=0.24.0

# For data processing and analysis
# pandas>=2.0.0
# numpy>=1.24.0

# For cloud storage integration (if extending beyond OSS)
# boto3>=1.28.0  # AWS S3
# azure-storage-blob>=12.17.0  # Azure Blob Storage
# google-cloud-storage>=2.10.0  # Google Cloud Storage

# For database storage (if extending to store metadata)
# sqlalchemy>=2.0.0
# psycopg2-binary>=2.9.0  # PostgreSQL
# pymongo>=4.5.0  # MongoDB

# For advanced scheduling (if extending to scheduled runs)
# schedule>=1.2.0
# celery>=5.3.0

# For configuration management
# pydantic>=2.0.0
# pyyaml>=6.0

# For testing (development only)
# pytest>=7.4.0
# pytest-cov>=4.1.0
# pytest-mock>=3.11.0
# requests-mock>=1.11.0
