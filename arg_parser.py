"""
Command-line argument parser for the Devo API backup system.
Handles parsing and validation of execution modes.
"""

import sys
from typing import Tuple, List, Optional
from enum import Enum

from date_utils import DateUtils
from logger import logger


class ExecutionMode(Enum):
    """Enumeration of execution modes."""
    DEFAULT = "default"          # Current day
    SPECIFIC_DATE = "specific"   # Single specific date
    DATE_RANGE = "range"         # Date range


class ArgumentParser:
    """Command-line argument parser for backup system."""
    
    def __init__(self):
        """Initialize the argument parser."""
        self.mode = None
        self.dates = []
        self.errors = []
    
    def parse_arguments(self, args: Optional[List[str]] = None) -> Tuple[ExecutionMode, List[str], List[str]]:
        """
        Parse command-line arguments and determine execution mode.
        
        Args:
            args: List of arguments (if None, uses sys.argv[1:])
            
        Returns:
            Tuple of (execution_mode, dates_list, errors_list)
        """
        if args is None:
            args = sys.argv[1:]
        
        self.errors = []
        
        # No arguments - default mode (current day)
        if len(args) == 0:
            current_date = DateUtils.get_current_date()
            logger.info(f"Default mode: Using current date {current_date}")
            return ExecutionMode.DEFAULT, [current_date], []
        
        # One argument - specific date mode
        elif len(args) == 1:
            date_str = args[0].strip()
            
            # Validate date format
            if not DateUtils.validate_date_string(date_str):
                error = f"Invalid date format: {date_str}. Expected YYYY-MM-DD format."
                self.errors.append(error)
                return ExecutionMode.SPECIFIC_DATE, [], [error]
            
            logger.info(f"Specific date mode: Using date {date_str}")
            return ExecutionMode.SPECIFIC_DATE, [date_str], []
        
        # Two arguments - date range mode
        elif len(args) == 2:
            start_date = args[0].strip()
            end_date = args[1].strip()
            
            # Validate date range
            is_valid, error_msg = DateUtils.validate_date_range(start_date, end_date)
            if not is_valid:
                self.errors.append(error_msg)
                return ExecutionMode.DATE_RANGE, [], [error_msg]
            
            # Generate date list
            try:
                date_list = DateUtils.generate_date_range(start_date, end_date)
                logger.info(f"Date range mode: {start_date} to {end_date} ({len(date_list)} days)")
                return ExecutionMode.DATE_RANGE, date_list, []
            except ValueError as e:
                error = str(e)
                self.errors.append(error)
                return ExecutionMode.DATE_RANGE, [], [error]
        
        # Too many arguments
        else:
            error = f"Too many arguments provided: {len(args)}. Expected 0, 1, or 2 arguments."
            self.errors.append(error)
            return ExecutionMode.DEFAULT, [], [error]
    
    def print_usage(self):
        """Print usage information."""
        usage_text = """
Devo API Data Backup System

Usage:
    python main.py                           # Default mode: Pull data for current day
    python main.py YYYY-MM-DD               # Specific date mode: Pull data for specific date
    python main.py YYYY-MM-DD YYYY-MM-DD    # Date range mode: Pull data for date range

Examples:
    python main.py                           # Pull data for today
    python main.py 2024-01-15               # Pull data for January 15, 2024
    python main.py 2024-01-15 2024-01-20    # Pull data from Jan 15 to Jan 20, 2024

Notes:
    - Dates must be in YYYY-MM-DD format
    - Start date must be before or equal to end date
    - Dates cannot be in the future
    - The system will pull all available data from configured Devo tables
        """
        print(usage_text)
    
    def validate_and_parse(self, args: Optional[List[str]] = None) -> Tuple[ExecutionMode, List[str]]:
        """
        Validate and parse arguments, handling errors gracefully.
        
        Args:
            args: List of arguments (if None, uses sys.argv[1:])
            
        Returns:
            Tuple of (execution_mode, dates_list)
            
        Raises:
            SystemExit: If arguments are invalid
        """
        mode, dates, errors = self.parse_arguments(args)
        
        if errors:
            logger.error("Argument parsing errors:")
            for error in errors:
                logger.error(f"  - {error}")
            
            print("\nERROR: Invalid arguments provided.")
            for error in errors:
                print(f"  - {error}")
            
            self.print_usage()
            sys.exit(1)
        
        return mode, dates
    
    def get_execution_summary(self, mode: ExecutionMode, dates: List[str]) -> str:
        """
        Get a summary of the execution plan.
        
        Args:
            mode: Execution mode
            dates: List of dates to process
            
        Returns:
            Summary string
        """
        if mode == ExecutionMode.DEFAULT:
            return f"Executing default mode for current date: {dates[0]}"
        elif mode == ExecutionMode.SPECIFIC_DATE:
            return f"Executing specific date mode for: {dates[0]}"
        elif mode == ExecutionMode.DATE_RANGE:
            return f"Executing date range mode: {dates[0]} to {dates[-1]} ({len(dates)} days)"
        else:
            return "Unknown execution mode"
    
    @staticmethod
    def print_help():
        """Print help information."""
        parser = ArgumentParser()
        parser.print_usage()


# Convenience function for quick parsing
def parse_args(args: Optional[List[str]] = None) -> Tuple[ExecutionMode, List[str]]:
    """
    Quick function to parse arguments.
    
    Args:
        args: List of arguments (if None, uses sys.argv[1:])
        
    Returns:
        Tuple of (execution_mode, dates_list)
    """
    parser = ArgumentParser()
    return parser.validate_and_parse(args)
