2025-07-03 13:19:06 - devo_backup - INFO - Logging initialized. Log file: logs/devo_backup_20250703_131906.log
2025-07-03 13:19:07 - devo_backup - INFO - Starting Devo Table Discovery...
2025-07-03 13:19:07 - devo_backup - INFO - Devo endpoint: https://api-apac.devo.com/search/query
2025-07-03 13:19:07 - devo_backup - INFO - Devo SDK client initialized with endpoint: https://api-apac.devo.com/search/query
2025-07-03 13:19:07 - devo_backup - INFO - Starting comprehensive table discovery...
2025-07-03 13:19:07 - devo_backup - INFO - Testing connection to Devo API...
2025-07-03 13:19:07 - devo_backup - INFO - Testing connection to Devo API...
2025-07-03 13:19:07 - devo_backup - INFO - Executing Devo query: from siem.logtrust.web.activity select count() as test_count...
2025-07-03 13:19:07 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:07 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.web.activity select count() as test_count...
2025-07-03 13:19:07 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:07 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:07 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:07 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:07 - devo_backup - INFO - Devo API connection test successful
2025-07-03 13:19:07 - devo_backup - INFO - Step 1: Discovering available tables...
2025-07-03 13:19:07 - devo_backup - INFO - Attempting to discover tables via system metadata...
2025-07-03 13:19:07 - devo_backup - DEBUG - Trying system query: from siem.logtrust.table.info select table
2025-07-03 13:19:07 - devo_backup - INFO - Executing Devo query: from siem.logtrust.table.info select table...
2025-07-03 13:19:07 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:07 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.table.info select table...
2025-07-03 13:19:07 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:07 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:07 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:07 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:07 - devo_backup - DEBUG - Trying system query: from system.table.metadata select table_name
2025-07-03 13:19:07 - devo_backup - INFO - Executing Devo query: from system.table.metadata select table_name...
2025-07-03 13:19:07 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:07 - devo_backup - DEBUG - Executing query (attempt 1): from system.table.metadata select table_name...
2025-07-03 13:19:07 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:07 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:07 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:07 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:07 - devo_backup - DEBUG - Trying system query: from devo.system.tables select name
2025-07-03 13:19:07 - devo_backup - INFO - Executing Devo query: from devo.system.tables select name...
2025-07-03 13:19:07 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:07 - devo_backup - DEBUG - Executing query (attempt 1): from devo.system.tables select name...
2025-07-03 13:19:07 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:07 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:07 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:07 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:07 - devo_backup - DEBUG - Trying system query: show tables
2025-07-03 13:19:07 - devo_backup - INFO - Executing Devo query: show tables...
2025-07-03 13:19:07 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:07 - devo_backup - DEBUG - Executing query (attempt 1): show tables...
2025-07-03 13:19:07 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:07 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:07 - devo_backup - DEBUG - Error consuming generator: Unable to Start Task: Query parsing error
2025-07-03 13:19:07 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:07 - devo_backup - DEBUG - Trying system query: describe tables
2025-07-03 13:19:07 - devo_backup - INFO - Executing Devo query: describe tables...
2025-07-03 13:19:07 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:07 - devo_backup - DEBUG - Executing query (attempt 1): describe tables...
2025-07-03 13:19:07 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:07 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:08 - devo_backup - DEBUG - Error consuming generator: Unable to Start Task: Query parsing error
2025-07-03 13:19:08 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:08 - devo_backup - INFO - Scanning common table patterns...
2025-07-03 13:19:08 - devo_backup - INFO - Testing 28 common table patterns...
2025-07-03 13:19:08 - devo_backup - INFO - Executing Devo query: from siem.logtrust.web.activity select count() as record_count...
2025-07-03 13:19:08 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:08 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.web.activity select count() as record_count...
2025-07-03 13:19:08 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:08 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:08 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:08 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:08 - devo_backup - DEBUG - Found table: siem.logtrust.web.activity
2025-07-03 13:19:08 - devo_backup - INFO - Executing Devo query: from siem.logtrust.alert.info select count() as record_count...
2025-07-03 13:19:08 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:08 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.alert.info select count() as record_count...
2025-07-03 13:19:08 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:08 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:08 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:08 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:08 - devo_backup - DEBUG - Found table: siem.logtrust.alert.info
2025-07-03 13:19:08 - devo_backup - INFO - Executing Devo query: from siem.logtrust.auth.info select count() as record_count...
2025-07-03 13:19:08 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:08 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.auth.info select count() as record_count...
2025-07-03 13:19:08 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:08 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:08 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:08 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:08 - devo_backup - DEBUG - Found table: siem.logtrust.auth.info
2025-07-03 13:19:08 - devo_backup - INFO - Executing Devo query: from siem.logtrust.dns.info select count() as record_count...
2025-07-03 13:19:08 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:08 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.dns.info select count() as record_count...
2025-07-03 13:19:08 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:08 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:08 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:08 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:08 - devo_backup - DEBUG - Found table: siem.logtrust.dns.info
2025-07-03 13:19:09 - devo_backup - INFO - Executing Devo query: from siem.logtrust.file.info select count() as record_count...
2025-07-03 13:19:09 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:09 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.file.info select count() as record_count...
2025-07-03 13:19:09 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:09 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:09 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:09 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:09 - devo_backup - DEBUG - Found table: siem.logtrust.file.info
2025-07-03 13:19:09 - devo_backup - INFO - Executing Devo query: from siem.logtrust.network.info select count() as record_count...
2025-07-03 13:19:09 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:09 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.network.info select count() as record_count...
2025-07-03 13:19:09 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:09 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:09 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:09 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:09 - devo_backup - DEBUG - Found table: siem.logtrust.network.info
2025-07-03 13:19:09 - devo_backup - INFO - Executing Devo query: from siem.logtrust.process.info select count() as record_count...
2025-07-03 13:19:09 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:09 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.process.info select count() as record_count...
2025-07-03 13:19:09 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:09 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:09 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:09 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:09 - devo_backup - DEBUG - Found table: siem.logtrust.process.info
2025-07-03 13:19:09 - devo_backup - INFO - Executing Devo query: from siem.logtrust.registry.info select count() as record_count...
2025-07-03 13:19:09 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:09 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.registry.info select count() as record_count...
2025-07-03 13:19:09 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:09 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:09 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:09 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:09 - devo_backup - DEBUG - Found table: siem.logtrust.registry.info
2025-07-03 13:19:09 - devo_backup - INFO - Executing Devo query: from siem.logtrust.system.info select count() as record_count...
2025-07-03 13:19:09 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:09 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.system.info select count() as record_count...
2025-07-03 13:19:09 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:09 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:10 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:10 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:10 - devo_backup - DEBUG - Found table: siem.logtrust.system.info
2025-07-03 13:19:10 - devo_backup - INFO - Executing Devo query: from firewall.all select count() as record_count...
2025-07-03 13:19:10 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:10 - devo_backup - DEBUG - Executing query (attempt 1): from firewall.all select count() as record_count...
2025-07-03 13:19:10 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:10 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:10 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:10 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:10 - devo_backup - DEBUG - Found table: firewall.all
2025-07-03 13:19:10 - devo_backup - INFO - Executing Devo query: from firewall.cisco.asa select count() as record_count...
2025-07-03 13:19:10 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:10 - devo_backup - DEBUG - Executing query (attempt 1): from firewall.cisco.asa select count() as record_count...
2025-07-03 13:19:10 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:10 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:10 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:10 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:10 - devo_backup - DEBUG - Found table: firewall.cisco.asa
2025-07-03 13:19:11 - devo_backup - INFO - Executing Devo query: from firewall.paloalto select count() as record_count...
2025-07-03 13:19:11 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:11 - devo_backup - DEBUG - Executing query (attempt 1): from firewall.paloalto select count() as record_count...
2025-07-03 13:19:11 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:11 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:11 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:11 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:11 - devo_backup - DEBUG - Found table: firewall.paloalto
2025-07-03 13:19:11 - devo_backup - INFO - Executing Devo query: from firewall.fortinet select count() as record_count...
2025-07-03 13:19:11 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:11 - devo_backup - DEBUG - Executing query (attempt 1): from firewall.fortinet select count() as record_count...
2025-07-03 13:19:11 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:11 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:11 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:11 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:11 - devo_backup - DEBUG - Found table: firewall.fortinet
2025-07-03 13:19:11 - devo_backup - INFO - Executing Devo query: from network.cisco.router select count() as record_count...
2025-07-03 13:19:11 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:11 - devo_backup - DEBUG - Executing query (attempt 1): from network.cisco.router select count() as record_count...
2025-07-03 13:19:11 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:11 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:11 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:11 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:11 - devo_backup - DEBUG - Found table: network.cisco.router
2025-07-03 13:19:11 - devo_backup - INFO - Executing Devo query: from network.switch.info select count() as record_count...
2025-07-03 13:19:11 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:11 - devo_backup - DEBUG - Executing query (attempt 1): from network.switch.info select count() as record_count...
2025-07-03 13:19:11 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:11 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:11 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:11 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:11 - devo_backup - DEBUG - Found table: network.switch.info
2025-07-03 13:19:11 - devo_backup - INFO - Executing Devo query: from app.web.access select count() as record_count...
2025-07-03 13:19:11 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:11 - devo_backup - DEBUG - Executing query (attempt 1): from app.web.access select count() as record_count...
2025-07-03 13:19:11 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:11 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:12 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:12 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:12 - devo_backup - DEBUG - Found table: app.web.access
2025-07-03 13:19:12 - devo_backup - INFO - Executing Devo query: from app.web.error select count() as record_count...
2025-07-03 13:19:12 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:12 - devo_backup - DEBUG - Executing query (attempt 1): from app.web.error select count() as record_count...
2025-07-03 13:19:12 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:12 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:12 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:12 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:12 - devo_backup - DEBUG - Found table: app.web.error
2025-07-03 13:19:12 - devo_backup - INFO - Executing Devo query: from app.database.audit select count() as record_count...
2025-07-03 13:19:12 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:12 - devo_backup - DEBUG - Executing query (attempt 1): from app.database.audit select count() as record_count...
2025-07-03 13:19:12 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:12 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:12 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:12 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:12 - devo_backup - DEBUG - Found table: app.database.audit
2025-07-03 13:19:12 - devo_backup - INFO - Executing Devo query: from app.api.requests select count() as record_count...
2025-07-03 13:19:12 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:12 - devo_backup - DEBUG - Executing query (attempt 1): from app.api.requests select count() as record_count...
2025-07-03 13:19:12 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:12 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:12 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:12 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:12 - devo_backup - DEBUG - Found table: app.api.requests
2025-07-03 13:19:12 - devo_backup - INFO - Executing Devo query: from system.windows.eventlog select count() as record_count...
2025-07-03 13:19:12 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:12 - devo_backup - DEBUG - Executing query (attempt 1): from system.windows.eventlog select count() as record_count...
2025-07-03 13:19:12 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:12 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:13 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:13 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:13 - devo_backup - DEBUG - Found table: system.windows.eventlog
2025-07-03 13:19:13 - devo_backup - INFO - Executing Devo query: from system.linux.syslog select count() as record_count...
2025-07-03 13:19:13 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:13 - devo_backup - DEBUG - Executing query (attempt 1): from system.linux.syslog select count() as record_count...
2025-07-03 13:19:13 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:13 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:13 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:13 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:13 - devo_backup - DEBUG - Found table: system.linux.syslog
2025-07-03 13:19:13 - devo_backup - INFO - Executing Devo query: from system.auth.login select count() as record_count...
2025-07-03 13:19:13 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:13 - devo_backup - DEBUG - Executing query (attempt 1): from system.auth.login select count() as record_count...
2025-07-03 13:19:13 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:13 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:13 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:13 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:13 - devo_backup - DEBUG - Found table: system.auth.login
2025-07-03 13:19:13 - devo_backup - INFO - Executing Devo query: from cloud.aws.cloudtrail select count() as record_count...
2025-07-03 13:19:13 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:13 - devo_backup - DEBUG - Executing query (attempt 1): from cloud.aws.cloudtrail select count() as record_count...
2025-07-03 13:19:13 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:13 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:13 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:13 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:13 - devo_backup - DEBUG - Found table: cloud.aws.cloudtrail
2025-07-03 13:19:13 - devo_backup - INFO - Executing Devo query: from cloud.azure.activity select count() as record_count...
2025-07-03 13:19:13 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:13 - devo_backup - DEBUG - Executing query (attempt 1): from cloud.azure.activity select count() as record_count...
2025-07-03 13:19:13 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:13 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:14 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:14 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:14 - devo_backup - DEBUG - Found table: cloud.azure.activity
2025-07-03 13:19:14 - devo_backup - INFO - Executing Devo query: from cloud.gcp.audit select count() as record_count...
2025-07-03 13:19:14 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:14 - devo_backup - DEBUG - Executing query (attempt 1): from cloud.gcp.audit select count() as record_count...
2025-07-03 13:19:14 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:14 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:14 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:14 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:14 - devo_backup - DEBUG - Found table: cloud.gcp.audit
2025-07-03 13:19:14 - devo_backup - INFO - Executing Devo query: from my.app.data select count() as record_count...
2025-07-03 13:19:14 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:14 - devo_backup - DEBUG - Executing query (attempt 1): from my.app.data select count() as record_count...
2025-07-03 13:19:14 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:14 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:14 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:14 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:14 - devo_backup - DEBUG - Found table: my.app.data
2025-07-03 13:19:14 - devo_backup - INFO - Executing Devo query: from custom.logs.info select count() as record_count...
2025-07-03 13:19:14 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:14 - devo_backup - DEBUG - Executing query (attempt 1): from custom.logs.info select count() as record_count...
2025-07-03 13:19:14 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:14 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:14 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:14 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:14 - devo_backup - DEBUG - Found table: custom.logs.info
2025-07-03 13:19:14 - devo_backup - INFO - Executing Devo query: from user.activity.log select count() as record_count...
2025-07-03 13:19:14 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:14 - devo_backup - DEBUG - Executing query (attempt 1): from user.activity.log select count() as record_count...
2025-07-03 13:19:14 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:14 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:14 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:14 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:14 - devo_backup - DEBUG - Found table: user.activity.log
2025-07-03 13:19:15 - devo_backup - INFO - Found 28 tables via pattern scanning
2025-07-03 13:19:15 - devo_backup - INFO - Found 28 additional tables via pattern scanning
2025-07-03 13:19:15 - devo_backup - INFO - Scanning known prefixes...
2025-07-03 13:19:15 - devo_backup - INFO - Scanning 15 known prefixes...
2025-07-03 13:19:15 - devo_backup - INFO - Total unique tables discovered: 28
2025-07-03 13:19:15 - devo_backup - INFO - Found 28 tables to analyze
2025-07-03 13:19:15 - devo_backup - INFO - Step 2: Analyzing table details...
2025-07-03 13:19:15 - devo_backup - INFO - Analyzing 28 tables for detailed information...
2025-07-03 13:19:15 - devo_backup - DEBUG - Analyzing table: app.api.requests
2025-07-03 13:19:15 - devo_backup - INFO - Executing Devo query: from app.api.requests select count() as record_count...
2025-07-03 13:19:15 - devo_backup - DEBUG - Analyzing table: app.database.audit
2025-07-03 13:19:15 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:15 - devo_backup - DEBUG - Analyzing table: app.web.access
2025-07-03 13:19:15 - devo_backup - INFO - Executing Devo query: from app.database.audit select count() as record_count...
2025-07-03 13:19:15 - devo_backup - DEBUG - Analyzing table: app.web.error
2025-07-03 13:19:15 - devo_backup - DEBUG - Executing query (attempt 1): from app.api.requests select count() as record_count...
2025-07-03 13:19:15 - devo_backup - DEBUG - Analyzing table: cloud.aws.cloudtrail
2025-07-03 13:19:15 - devo_backup - INFO - Executing Devo query: from app.web.access select count() as record_count...
2025-07-03 13:19:15 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:15 - devo_backup - INFO - Executing Devo query: from app.web.error select count() as record_count...
2025-07-03 13:19:15 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:15 - devo_backup - INFO - Executing Devo query: from cloud.aws.cloudtrail select count() as record_count...
2025-07-03 13:19:15 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:15 - devo_backup - DEBUG - Executing query (attempt 1): from app.database.audit select count() as record_count...
2025-07-03 13:19:15 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:15 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:15 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:15 - devo_backup - DEBUG - Executing query (attempt 1): from app.web.access select count() as record_count...
2025-07-03 13:19:15 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:15 - devo_backup - DEBUG - Executing query (attempt 1): from app.web.error select count() as record_count...
2025-07-03 13:19:15 - devo_backup - DEBUG - Executing query (attempt 1): from cloud.aws.cloudtrail select count() as record_count...
2025-07-03 13:19:15 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:15 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:15 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:15 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:15 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:15 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:15 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:15 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:15 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:15 - devo_backup - INFO - Executing Devo query: from app.web.access select * limit 5...
2025-07-03 13:19:15 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:15 - devo_backup - DEBUG - Executing query (attempt 1): from app.web.access select * limit 5...
2025-07-03 13:19:15 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:15 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:15 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:15 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:15 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:15 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:15 - devo_backup - INFO - Executing Devo query: from app.api.requests select * limit 5...
2025-07-03 13:19:15 - devo_backup - INFO - Executing Devo query: from app.database.audit select * limit 5...
2025-07-03 13:19:15 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:15 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:15 - devo_backup - DEBUG - Executing query (attempt 1): from app.api.requests select * limit 5...
2025-07-03 13:19:15 - devo_backup - DEBUG - Executing query (attempt 1): from app.database.audit select * limit 5...
2025-07-03 13:19:15 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:15 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:15 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:15 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:15 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:15 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:15 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:15 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:15 - devo_backup - INFO - Executing Devo query: from cloud.aws.cloudtrail select * limit 5...
2025-07-03 13:19:15 - devo_backup - INFO - Executing Devo query: from app.web.error select * limit 5...
2025-07-03 13:19:15 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:15 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:15 - devo_backup - DEBUG - Executing query (attempt 1): from cloud.aws.cloudtrail select * limit 5...
2025-07-03 13:19:15 - devo_backup - DEBUG - Executing query (attempt 1): from app.web.error select * limit 5...
2025-07-03 13:19:15 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:15 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:15 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:15 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:15 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:15 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:15 - devo_backup - INFO - Executing Devo query: from app.web.access select * limit 3...
2025-07-03 13:19:15 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:15 - devo_backup - DEBUG - Executing query (attempt 1): from app.web.access select * limit 3...
2025-07-03 13:19:15 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:15 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:15 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:15 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:15 - devo_backup - INFO - Executing Devo query: from app.api.requests select * limit 3...
2025-07-03 13:19:15 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:15 - devo_backup - DEBUG - Executing query (attempt 1): from app.api.requests select * limit 3...
2025-07-03 13:19:15 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:15 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:15 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:15 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:15 - devo_backup - INFO - Executing Devo query: from app.database.audit select * limit 3...
2025-07-03 13:19:15 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:15 - devo_backup - DEBUG - Executing query (attempt 1): from app.database.audit select * limit 3...
2025-07-03 13:19:15 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:15 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:15 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:15 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:15 - devo_backup - INFO - Executing Devo query: from cloud.aws.cloudtrail select * limit 3...
2025-07-03 13:19:15 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:15 - devo_backup - DEBUG - Executing query (attempt 1): from cloud.aws.cloudtrail select * limit 3...
2025-07-03 13:19:15 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:15 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:15 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:15 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:15 - devo_backup - INFO - Executing Devo query: from app.web.error select * limit 3...
2025-07-03 13:19:15 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:15 - devo_backup - DEBUG - Executing query (attempt 1): from app.web.error select * limit 3...
2025-07-03 13:19:15 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:15 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:15 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:15 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:15 - devo_backup - INFO - Executing Devo query: from app.web.access select max(timestamp) as last_update...
2025-07-03 13:19:15 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:15 - devo_backup - DEBUG - Executing query (attempt 1): from app.web.access select max(timestamp) as last_update...
2025-07-03 13:19:15 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:15 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:15 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:15 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:15 - devo_backup - INFO - Executing Devo query: from cloud.aws.cloudtrail select max(timestamp) as last_update...
2025-07-03 13:19:15 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:15 - devo_backup - DEBUG - Executing query (attempt 1): from cloud.aws.cloudtrail select max(timestamp) as last_update...
2025-07-03 13:19:15 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:15 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:15 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:15 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:15 - devo_backup - INFO - Executing Devo query: from app.database.audit select max(timestamp) as last_update...
2025-07-03 13:19:15 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:15 - devo_backup - DEBUG - Executing query (attempt 1): from app.database.audit select max(timestamp) as last_update...
2025-07-03 13:19:15 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:15 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:15 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:15 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:15 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:15 - devo_backup - INFO - Executing Devo query: from app.api.requests select max(timestamp) as last_update...
2025-07-03 13:19:15 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:15 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:15 - devo_backup - INFO - Executing Devo query: from app.web.error select max(timestamp) as last_update...
2025-07-03 13:19:15 - devo_backup - DEBUG - Executing query (attempt 1): from app.api.requests select max(timestamp) as last_update...
2025-07-03 13:19:15 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:15 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:15 - devo_backup - DEBUG - Executing query (attempt 1): from app.web.error select max(timestamp) as last_update...
2025-07-03 13:19:15 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:15 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:15 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:15 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:15 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:15 - devo_backup - INFO - Executing Devo query: from app.web.access select max(eventdate) as last_update...
2025-07-03 13:19:15 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:15 - devo_backup - DEBUG - Executing query (attempt 1): from app.web.access select max(eventdate) as last_update...
2025-07-03 13:19:15 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:15 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:15 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:15 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:15 - devo_backup - INFO - Executing Devo query: from app.database.audit select max(eventdate) as last_update...
2025-07-03 13:19:15 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:15 - devo_backup - DEBUG - Executing query (attempt 1): from app.database.audit select max(eventdate) as last_update...
2025-07-03 13:19:15 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:15 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:15 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:15 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:15 - devo_backup - INFO - Executing Devo query: from cloud.aws.cloudtrail select max(eventdate) as last_update...
2025-07-03 13:19:15 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:15 - devo_backup - DEBUG - Executing query (attempt 1): from cloud.aws.cloudtrail select max(eventdate) as last_update...
2025-07-03 13:19:15 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:15 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:15 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:15 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:15 - devo_backup - INFO - Executing Devo query: from app.api.requests select max(eventdate) as last_update...
2025-07-03 13:19:15 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:15 - devo_backup - DEBUG - Executing query (attempt 1): from app.api.requests select max(eventdate) as last_update...
2025-07-03 13:19:15 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:15 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:15 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:15 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:15 - devo_backup - INFO - Executing Devo query: from app.web.error select max(eventdate) as last_update...
2025-07-03 13:19:15 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:15 - devo_backup - DEBUG - Executing query (attempt 1): from app.web.error select max(eventdate) as last_update...
2025-07-03 13:19:15 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:15 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:15 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:15 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:15 - devo_backup - INFO - Executing Devo query: from app.web.access select max(date) as last_update...
2025-07-03 13:19:15 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:15 - devo_backup - DEBUG - Executing query (attempt 1): from app.web.access select max(date) as last_update...
2025-07-03 13:19:15 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:15 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:15 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:15 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:15 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:15 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:15 - devo_backup - INFO - Executing Devo query: from cloud.aws.cloudtrail select max(date) as last_update...
2025-07-03 13:19:15 - devo_backup - INFO - Executing Devo query: from app.database.audit select max(date) as last_update...
2025-07-03 13:19:15 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:15 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:15 - devo_backup - DEBUG - Executing query (attempt 1): from cloud.aws.cloudtrail select max(date) as last_update...
2025-07-03 13:19:15 - devo_backup - DEBUG - Executing query (attempt 1): from app.database.audit select max(date) as last_update...
2025-07-03 13:19:15 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:15 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:15 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:15 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:15 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:15 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:15 - devo_backup - INFO - Executing Devo query: from app.web.error select max(date) as last_update...
2025-07-03 13:19:15 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:15 - devo_backup - DEBUG - Executing query (attempt 1): from app.web.error select max(date) as last_update...
2025-07-03 13:19:15 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:15 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:15 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:15 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:15 - devo_backup - INFO - Executing Devo query: from app.api.requests select max(date) as last_update...
2025-07-03 13:19:15 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:15 - devo_backup - DEBUG - Executing query (attempt 1): from app.api.requests select max(date) as last_update...
2025-07-03 13:19:15 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:15 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:15 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:15 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:15 - devo_backup - INFO - Executing Devo query: from app.database.audit select max(time) as last_update...
2025-07-03 13:19:15 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:15 - devo_backup - DEBUG - Executing query (attempt 1): from app.database.audit select max(time) as last_update...
2025-07-03 13:19:15 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:15 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:15 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:15 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:15 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:15 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:15 - devo_backup - INFO - Executing Devo query: from cloud.aws.cloudtrail select max(time) as last_update...
2025-07-03 13:19:15 - devo_backup - INFO - Executing Devo query: from app.web.access select max(time) as last_update...
2025-07-03 13:19:15 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:15 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:15 - devo_backup - DEBUG - Executing query (attempt 1): from cloud.aws.cloudtrail select max(time) as last_update...
2025-07-03 13:19:15 - devo_backup - DEBUG - Executing query (attempt 1): from app.web.access select max(time) as last_update...
2025-07-03 13:19:15 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:15 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:15 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:15 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:15 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:15 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:15 - devo_backup - INFO - Executing Devo query: from app.web.error select max(time) as last_update...
2025-07-03 13:19:15 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:15 - devo_backup - DEBUG - Executing query (attempt 1): from app.web.error select max(time) as last_update...
2025-07-03 13:19:15 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:15 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:15 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:15 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:15 - devo_backup - INFO - Executing Devo query: from app.api.requests select max(time) as last_update...
2025-07-03 13:19:15 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:15 - devo_backup - DEBUG - Executing query (attempt 1): from app.api.requests select max(time) as last_update...
2025-07-03 13:19:15 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:15 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:16 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:16 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:16 - devo_backup - INFO - Executing Devo query: from app.database.audit select max(_time) as last_update...
2025-07-03 13:19:16 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:16 - devo_backup - DEBUG - Executing query (attempt 1): from app.database.audit select max(_time) as last_update...
2025-07-03 13:19:16 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:16 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:16 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:16 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:16 - devo_backup - INFO - Executing Devo query: from cloud.aws.cloudtrail select max(_time) as last_update...
2025-07-03 13:19:16 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:16 - devo_backup - DEBUG - Executing query (attempt 1): from cloud.aws.cloudtrail select max(_time) as last_update...
2025-07-03 13:19:16 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:16 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:16 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:16 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:16 - devo_backup - INFO - Executing Devo query: from app.web.access select max(_time) as last_update...
2025-07-03 13:19:16 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:16 - devo_backup - DEBUG - Executing query (attempt 1): from app.web.access select max(_time) as last_update...
2025-07-03 13:19:16 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:16 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:16 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:16 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:16 - devo_backup - INFO - Executing Devo query: from app.web.error select max(_time) as last_update...
2025-07-03 13:19:16 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:16 - devo_backup - DEBUG - Executing query (attempt 1): from app.web.error select max(_time) as last_update...
2025-07-03 13:19:16 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:16 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:16 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:16 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:16 - devo_backup - INFO - Executing Devo query: from app.api.requests select max(_time) as last_update...
2025-07-03 13:19:16 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:16 - devo_backup - DEBUG - Executing query (attempt 1): from app.api.requests select max(_time) as last_update...
2025-07-03 13:19:16 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:16 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:16 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:16 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:16 - devo_backup - INFO - Executing Devo query: from app.database.audit select max(created_at) as last_update...
2025-07-03 13:19:16 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:16 - devo_backup - DEBUG - Executing query (attempt 1): from app.database.audit select max(created_at) as last_update...
2025-07-03 13:19:16 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:16 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:16 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:16 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:16 - devo_backup - INFO - Executing Devo query: from app.web.access select max(created_at) as last_update...
2025-07-03 13:19:16 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:16 - devo_backup - DEBUG - Executing query (attempt 1): from app.web.access select max(created_at) as last_update...
2025-07-03 13:19:16 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:16 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:16 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:16 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:16 - devo_backup - INFO - Executing Devo query: from cloud.aws.cloudtrail select max(created_at) as last_update...
2025-07-03 13:19:16 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:16 - devo_backup - DEBUG - Executing query (attempt 1): from cloud.aws.cloudtrail select max(created_at) as last_update...
2025-07-03 13:19:16 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:16 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:16 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:16 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:16 - devo_backup - INFO - Executing Devo query: from app.api.requests select max(created_at) as last_update...
2025-07-03 13:19:16 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:16 - devo_backup - DEBUG - Executing query (attempt 1): from app.api.requests select max(created_at) as last_update...
2025-07-03 13:19:16 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:16 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:16 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:16 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:16 - devo_backup - INFO - Executing Devo query: from app.web.error select max(created_at) as last_update...
2025-07-03 13:19:16 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:16 - devo_backup - DEBUG - Executing query (attempt 1): from app.web.error select max(created_at) as last_update...
2025-07-03 13:19:16 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:16 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:16 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:16 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:16 - devo_backup - INFO - Executing Devo query: from app.database.audit select max(updated_at) as last_update...
2025-07-03 13:19:16 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:16 - devo_backup - DEBUG - Executing query (attempt 1): from app.database.audit select max(updated_at) as last_update...
2025-07-03 13:19:16 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:16 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:16 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:16 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:16 - devo_backup - INFO - Executing Devo query: from app.web.access select max(updated_at) as last_update...
2025-07-03 13:19:16 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:16 - devo_backup - DEBUG - Executing query (attempt 1): from app.web.access select max(updated_at) as last_update...
2025-07-03 13:19:16 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:16 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:16 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:16 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:16 - devo_backup - INFO - Executing Devo query: from cloud.aws.cloudtrail select max(updated_at) as last_update...
2025-07-03 13:19:16 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:16 - devo_backup - DEBUG - Executing query (attempt 1): from cloud.aws.cloudtrail select max(updated_at) as last_update...
2025-07-03 13:19:16 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:16 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:16 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:16 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:16 - devo_backup - INFO - Executing Devo query: from app.api.requests select max(updated_at) as last_update...
2025-07-03 13:19:16 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:16 - devo_backup - DEBUG - Executing query (attempt 1): from app.api.requests select max(updated_at) as last_update...
2025-07-03 13:19:16 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:16 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:16 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:16 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:16 - devo_backup - INFO - Executing Devo query: from app.web.error select max(updated_at) as last_update...
2025-07-03 13:19:16 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:16 - devo_backup - DEBUG - Executing query (attempt 1): from app.web.error select max(updated_at) as last_update...
2025-07-03 13:19:16 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:16 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:16 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:16 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:16 - devo_backup - DEBUG - Successfully analyzed table: app.database.audit
2025-07-03 13:19:16 - devo_backup - DEBUG - Analyzing table: cloud.azure.activity
2025-07-03 13:19:16 - devo_backup - INFO - Executing Devo query: from cloud.azure.activity select count() as record_count...
2025-07-03 13:19:16 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:16 - devo_backup - DEBUG - Executing query (attempt 1): from cloud.azure.activity select count() as record_count...
2025-07-03 13:19:16 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:16 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:16 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:16 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:16 - devo_backup - DEBUG - Successfully analyzed table: app.web.access
2025-07-03 13:19:16 - devo_backup - DEBUG - Analyzing table: cloud.gcp.audit
2025-07-03 13:19:16 - devo_backup - INFO - Executing Devo query: from cloud.gcp.audit select count() as record_count...
2025-07-03 13:19:16 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:16 - devo_backup - DEBUG - Executing query (attempt 1): from cloud.gcp.audit select count() as record_count...
2025-07-03 13:19:16 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:16 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:16 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:16 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:16 - devo_backup - DEBUG - Successfully analyzed table: app.api.requests
2025-07-03 13:19:16 - devo_backup - DEBUG - Analyzing table: custom.logs.info
2025-07-03 13:19:16 - devo_backup - INFO - Executing Devo query: from custom.logs.info select count() as record_count...
2025-07-03 13:19:16 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:16 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:16 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:16 - devo_backup - DEBUG - Executing query (attempt 1): from custom.logs.info select count() as record_count...
2025-07-03 13:19:16 - devo_backup - DEBUG - Successfully analyzed table: cloud.aws.cloudtrail
2025-07-03 13:19:16 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:16 - devo_backup - DEBUG - Analyzing table: firewall.all
2025-07-03 13:19:16 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:16 - devo_backup - INFO - Executing Devo query: from firewall.all select count() as record_count...
2025-07-03 13:19:16 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:16 - devo_backup - DEBUG - Executing query (attempt 1): from firewall.all select count() as record_count...
2025-07-03 13:19:16 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:16 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:16 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:16 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:16 - devo_backup - DEBUG - Successfully analyzed table: app.web.error
2025-07-03 13:19:16 - devo_backup - DEBUG - Analyzing table: firewall.cisco.asa
2025-07-03 13:19:16 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:16 - devo_backup - INFO - Executing Devo query: from firewall.cisco.asa select count() as record_count...
2025-07-03 13:19:16 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:16 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:16 - devo_backup - INFO - Executing Devo query: from cloud.azure.activity select * limit 5...
2025-07-03 13:19:16 - devo_backup - DEBUG - Executing query (attempt 1): from firewall.cisco.asa select count() as record_count...
2025-07-03 13:19:16 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:16 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:16 - devo_backup - DEBUG - Executing query (attempt 1): from cloud.azure.activity select * limit 5...
2025-07-03 13:19:16 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:16 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:16 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:16 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:16 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:16 - devo_backup - INFO - Executing Devo query: from cloud.gcp.audit select * limit 5...
2025-07-03 13:19:16 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:16 - devo_backup - DEBUG - Executing query (attempt 1): from cloud.gcp.audit select * limit 5...
2025-07-03 13:19:16 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:16 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:16 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:16 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:16 - devo_backup - INFO - Executing Devo query: from firewall.all select * limit 5...
2025-07-03 13:19:16 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:16 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:16 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:16 - devo_backup - DEBUG - Executing query (attempt 1): from firewall.all select * limit 5...
2025-07-03 13:19:16 - devo_backup - INFO - Executing Devo query: from custom.logs.info select * limit 5...
2025-07-03 13:19:16 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:16 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:16 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:16 - devo_backup - DEBUG - Executing query (attempt 1): from custom.logs.info select * limit 5...
2025-07-03 13:19:16 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:16 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:16 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:16 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:16 - devo_backup - INFO - Executing Devo query: from cloud.azure.activity select * limit 3...
2025-07-03 13:19:16 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:16 - devo_backup - DEBUG - Executing query (attempt 1): from cloud.azure.activity select * limit 3...
2025-07-03 13:19:16 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:16 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:16 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:16 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:16 - devo_backup - INFO - Executing Devo query: from firewall.cisco.asa select * limit 5...
2025-07-03 13:19:16 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:16 - devo_backup - DEBUG - Executing query (attempt 1): from firewall.cisco.asa select * limit 5...
2025-07-03 13:19:16 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:16 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:16 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:16 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:16 - devo_backup - INFO - Executing Devo query: from cloud.gcp.audit select * limit 3...
2025-07-03 13:19:16 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:16 - devo_backup - DEBUG - Executing query (attempt 1): from cloud.gcp.audit select * limit 3...
2025-07-03 13:19:16 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:16 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:16 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:16 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:16 - devo_backup - INFO - Executing Devo query: from firewall.all select * limit 3...
2025-07-03 13:19:16 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:16 - devo_backup - DEBUG - Executing query (attempt 1): from firewall.all select * limit 3...
2025-07-03 13:19:16 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:16 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:16 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:16 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:16 - devo_backup - INFO - Executing Devo query: from custom.logs.info select * limit 3...
2025-07-03 13:19:16 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:16 - devo_backup - DEBUG - Executing query (attempt 1): from custom.logs.info select * limit 3...
2025-07-03 13:19:16 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:16 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:16 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:16 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:16 - devo_backup - INFO - Executing Devo query: from cloud.azure.activity select max(timestamp) as last_update...
2025-07-03 13:19:16 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:16 - devo_backup - DEBUG - Executing query (attempt 1): from cloud.azure.activity select max(timestamp) as last_update...
2025-07-03 13:19:16 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:16 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:16 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:16 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:16 - devo_backup - INFO - Executing Devo query: from firewall.cisco.asa select * limit 3...
2025-07-03 13:19:16 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:16 - devo_backup - DEBUG - Executing query (attempt 1): from firewall.cisco.asa select * limit 3...
2025-07-03 13:19:16 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:16 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:16 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:16 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:16 - devo_backup - INFO - Executing Devo query: from cloud.gcp.audit select max(timestamp) as last_update...
2025-07-03 13:19:16 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:16 - devo_backup - DEBUG - Executing query (attempt 1): from cloud.gcp.audit select max(timestamp) as last_update...
2025-07-03 13:19:16 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:16 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:16 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:16 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:16 - devo_backup - INFO - Executing Devo query: from firewall.all select max(timestamp) as last_update...
2025-07-03 13:19:16 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:16 - devo_backup - DEBUG - Executing query (attempt 1): from firewall.all select max(timestamp) as last_update...
2025-07-03 13:19:16 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:16 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:16 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:16 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:16 - devo_backup - INFO - Executing Devo query: from custom.logs.info select max(timestamp) as last_update...
2025-07-03 13:19:16 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:16 - devo_backup - DEBUG - Executing query (attempt 1): from custom.logs.info select max(timestamp) as last_update...
2025-07-03 13:19:16 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:16 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:16 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:16 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:16 - devo_backup - INFO - Executing Devo query: from cloud.azure.activity select max(eventdate) as last_update...
2025-07-03 13:19:16 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:16 - devo_backup - DEBUG - Executing query (attempt 1): from cloud.azure.activity select max(eventdate) as last_update...
2025-07-03 13:19:16 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:16 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:16 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:16 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:16 - devo_backup - INFO - Executing Devo query: from firewall.cisco.asa select max(timestamp) as last_update...
2025-07-03 13:19:16 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:16 - devo_backup - DEBUG - Executing query (attempt 1): from firewall.cisco.asa select max(timestamp) as last_update...
2025-07-03 13:19:16 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:17 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:17 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:17 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:17 - devo_backup - INFO - Executing Devo query: from cloud.gcp.audit select max(eventdate) as last_update...
2025-07-03 13:19:17 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:17 - devo_backup - DEBUG - Executing query (attempt 1): from cloud.gcp.audit select max(eventdate) as last_update...
2025-07-03 13:19:17 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:17 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:17 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:17 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:17 - devo_backup - INFO - Executing Devo query: from custom.logs.info select max(eventdate) as last_update...
2025-07-03 13:19:17 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:17 - devo_backup - DEBUG - Executing query (attempt 1): from custom.logs.info select max(eventdate) as last_update...
2025-07-03 13:19:17 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:17 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:17 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:17 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:17 - devo_backup - INFO - Executing Devo query: from firewall.all select max(eventdate) as last_update...
2025-07-03 13:19:17 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:17 - devo_backup - DEBUG - Executing query (attempt 1): from firewall.all select max(eventdate) as last_update...
2025-07-03 13:19:17 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:17 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:17 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:17 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:17 - devo_backup - INFO - Executing Devo query: from cloud.azure.activity select max(date) as last_update...
2025-07-03 13:19:17 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:17 - devo_backup - DEBUG - Executing query (attempt 1): from cloud.azure.activity select max(date) as last_update...
2025-07-03 13:19:17 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:17 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:17 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:17 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:17 - devo_backup - INFO - Executing Devo query: from firewall.cisco.asa select max(eventdate) as last_update...
2025-07-03 13:19:17 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:17 - devo_backup - DEBUG - Executing query (attempt 1): from firewall.cisco.asa select max(eventdate) as last_update...
2025-07-03 13:19:17 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:17 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:17 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:17 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:17 - devo_backup - INFO - Executing Devo query: from cloud.gcp.audit select max(date) as last_update...
2025-07-03 13:19:17 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:17 - devo_backup - DEBUG - Executing query (attempt 1): from cloud.gcp.audit select max(date) as last_update...
2025-07-03 13:19:17 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:17 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:17 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:17 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:17 - devo_backup - INFO - Executing Devo query: from firewall.all select max(date) as last_update...
2025-07-03 13:19:17 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:17 - devo_backup - DEBUG - Executing query (attempt 1): from firewall.all select max(date) as last_update...
2025-07-03 13:19:17 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:17 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:17 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:17 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:17 - devo_backup - INFO - Executing Devo query: from custom.logs.info select max(date) as last_update...
2025-07-03 13:19:17 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:17 - devo_backup - DEBUG - Executing query (attempt 1): from custom.logs.info select max(date) as last_update...
2025-07-03 13:19:17 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:17 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:17 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:17 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:17 - devo_backup - INFO - Executing Devo query: from cloud.azure.activity select max(time) as last_update...
2025-07-03 13:19:17 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:17 - devo_backup - DEBUG - Executing query (attempt 1): from cloud.azure.activity select max(time) as last_update...
2025-07-03 13:19:17 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:17 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:17 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:17 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:17 - devo_backup - INFO - Executing Devo query: from firewall.cisco.asa select max(date) as last_update...
2025-07-03 13:19:17 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:17 - devo_backup - DEBUG - Executing query (attempt 1): from firewall.cisco.asa select max(date) as last_update...
2025-07-03 13:19:17 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:17 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:17 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:17 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:17 - devo_backup - INFO - Executing Devo query: from cloud.gcp.audit select max(time) as last_update...
2025-07-03 13:19:17 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:17 - devo_backup - DEBUG - Executing query (attempt 1): from cloud.gcp.audit select max(time) as last_update...
2025-07-03 13:19:17 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:17 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:17 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:17 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:17 - devo_backup - INFO - Executing Devo query: from firewall.all select max(time) as last_update...
2025-07-03 13:19:17 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:17 - devo_backup - DEBUG - Executing query (attempt 1): from firewall.all select max(time) as last_update...
2025-07-03 13:19:17 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:17 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:17 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:17 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:17 - devo_backup - INFO - Executing Devo query: from custom.logs.info select max(time) as last_update...
2025-07-03 13:19:17 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:17 - devo_backup - DEBUG - Executing query (attempt 1): from custom.logs.info select max(time) as last_update...
2025-07-03 13:19:17 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:17 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:17 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:17 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:17 - devo_backup - INFO - Executing Devo query: from cloud.azure.activity select max(_time) as last_update...
2025-07-03 13:19:17 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:17 - devo_backup - DEBUG - Executing query (attempt 1): from cloud.azure.activity select max(_time) as last_update...
2025-07-03 13:19:17 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:17 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:17 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:17 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:17 - devo_backup - INFO - Executing Devo query: from firewall.cisco.asa select max(time) as last_update...
2025-07-03 13:19:17 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:17 - devo_backup - DEBUG - Executing query (attempt 1): from firewall.cisco.asa select max(time) as last_update...
2025-07-03 13:19:17 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:17 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:17 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:17 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:17 - devo_backup - INFO - Executing Devo query: from cloud.gcp.audit select max(_time) as last_update...
2025-07-03 13:19:17 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:17 - devo_backup - DEBUG - Executing query (attempt 1): from cloud.gcp.audit select max(_time) as last_update...
2025-07-03 13:19:17 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:17 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:17 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:17 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:17 - devo_backup - INFO - Executing Devo query: from custom.logs.info select max(_time) as last_update...
2025-07-03 13:19:17 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:17 - devo_backup - DEBUG - Executing query (attempt 1): from custom.logs.info select max(_time) as last_update...
2025-07-03 13:19:17 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:17 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:17 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:17 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:17 - devo_backup - INFO - Executing Devo query: from firewall.all select max(_time) as last_update...
2025-07-03 13:19:17 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:17 - devo_backup - DEBUG - Executing query (attempt 1): from firewall.all select max(_time) as last_update...
2025-07-03 13:19:17 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:17 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:17 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:17 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:17 - devo_backup - INFO - Executing Devo query: from cloud.azure.activity select max(created_at) as last_update...
2025-07-03 13:19:17 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:17 - devo_backup - DEBUG - Executing query (attempt 1): from cloud.azure.activity select max(created_at) as last_update...
2025-07-03 13:19:17 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:17 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:17 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:17 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:17 - devo_backup - INFO - Executing Devo query: from firewall.cisco.asa select max(_time) as last_update...
2025-07-03 13:19:17 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:17 - devo_backup - DEBUG - Executing query (attempt 1): from firewall.cisco.asa select max(_time) as last_update...
2025-07-03 13:19:17 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:17 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:17 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:17 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:17 - devo_backup - INFO - Executing Devo query: from cloud.gcp.audit select max(created_at) as last_update...
2025-07-03 13:19:17 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:17 - devo_backup - DEBUG - Executing query (attempt 1): from cloud.gcp.audit select max(created_at) as last_update...
2025-07-03 13:19:17 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:17 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:17 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:17 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:17 - devo_backup - INFO - Executing Devo query: from firewall.all select max(created_at) as last_update...
2025-07-03 13:19:17 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:17 - devo_backup - DEBUG - Executing query (attempt 1): from firewall.all select max(created_at) as last_update...
2025-07-03 13:19:17 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:17 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:17 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:17 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:17 - devo_backup - INFO - Executing Devo query: from custom.logs.info select max(created_at) as last_update...
2025-07-03 13:19:17 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:17 - devo_backup - DEBUG - Executing query (attempt 1): from custom.logs.info select max(created_at) as last_update...
2025-07-03 13:19:17 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:17 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:17 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:17 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:17 - devo_backup - INFO - Executing Devo query: from cloud.azure.activity select max(updated_at) as last_update...
2025-07-03 13:19:17 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:17 - devo_backup - DEBUG - Executing query (attempt 1): from cloud.azure.activity select max(updated_at) as last_update...
2025-07-03 13:19:17 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:17 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:17 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:17 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:17 - devo_backup - INFO - Executing Devo query: from firewall.cisco.asa select max(created_at) as last_update...
2025-07-03 13:19:17 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:17 - devo_backup - DEBUG - Executing query (attempt 1): from firewall.cisco.asa select max(created_at) as last_update...
2025-07-03 13:19:17 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:17 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:17 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:17 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:17 - devo_backup - INFO - Executing Devo query: from cloud.gcp.audit select max(updated_at) as last_update...
2025-07-03 13:19:17 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:17 - devo_backup - DEBUG - Executing query (attempt 1): from cloud.gcp.audit select max(updated_at) as last_update...
2025-07-03 13:19:17 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:17 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:17 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:17 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:17 - devo_backup - INFO - Executing Devo query: from custom.logs.info select max(updated_at) as last_update...
2025-07-03 13:19:17 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:17 - devo_backup - DEBUG - Executing query (attempt 1): from custom.logs.info select max(updated_at) as last_update...
2025-07-03 13:19:17 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:17 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:17 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:17 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:17 - devo_backup - INFO - Executing Devo query: from firewall.all select max(updated_at) as last_update...
2025-07-03 13:19:17 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:17 - devo_backup - DEBUG - Executing query (attempt 1): from firewall.all select max(updated_at) as last_update...
2025-07-03 13:19:17 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:17 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:17 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:17 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:17 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:17 - devo_backup - DEBUG - Successfully analyzed table: cloud.azure.activity
2025-07-03 13:19:17 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:17 - devo_backup - DEBUG - Analyzing table: firewall.fortinet
2025-07-03 13:19:17 - devo_backup - INFO - Executing Devo query: from firewall.cisco.asa select max(updated_at) as last_update...
2025-07-03 13:19:17 - devo_backup - INFO - Executing Devo query: from firewall.fortinet select count() as record_count...
2025-07-03 13:19:17 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:17 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:17 - devo_backup - DEBUG - Executing query (attempt 1): from firewall.cisco.asa select max(updated_at) as last_update...
2025-07-03 13:19:17 - devo_backup - DEBUG - Executing query (attempt 1): from firewall.fortinet select count() as record_count...
2025-07-03 13:19:17 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:17 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:17 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:17 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:17 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:17 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:17 - devo_backup - DEBUG - Successfully analyzed table: cloud.gcp.audit
2025-07-03 13:19:17 - devo_backup - DEBUG - Analyzing table: firewall.paloalto
2025-07-03 13:19:17 - devo_backup - INFO - Executing Devo query: from firewall.paloalto select count() as record_count...
2025-07-03 13:19:17 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:17 - devo_backup - DEBUG - Executing query (attempt 1): from firewall.paloalto select count() as record_count...
2025-07-03 13:19:17 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:17 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:17 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:17 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:17 - devo_backup - DEBUG - Successfully analyzed table: custom.logs.info
2025-07-03 13:19:17 - devo_backup - DEBUG - Analyzing table: my.app.data
2025-07-03 13:19:17 - devo_backup - INFO - Executing Devo query: from my.app.data select count() as record_count...
2025-07-03 13:19:17 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:17 - devo_backup - DEBUG - Executing query (attempt 1): from my.app.data select count() as record_count...
2025-07-03 13:19:17 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:17 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:17 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:17 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:17 - devo_backup - DEBUG - Successfully analyzed table: firewall.all
2025-07-03 13:19:17 - devo_backup - DEBUG - Analyzing table: network.cisco.router
2025-07-03 13:19:17 - devo_backup - INFO - Executing Devo query: from network.cisco.router select count() as record_count...
2025-07-03 13:19:17 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:17 - devo_backup - DEBUG - Executing query (attempt 1): from network.cisco.router select count() as record_count...
2025-07-03 13:19:17 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:17 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:17 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:17 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:17 - devo_backup - INFO - Executing Devo query: from firewall.fortinet select * limit 5...
2025-07-03 13:19:17 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:17 - devo_backup - DEBUG - Executing query (attempt 1): from firewall.fortinet select * limit 5...
2025-07-03 13:19:17 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:17 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:17 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:17 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:17 - devo_backup - DEBUG - Successfully analyzed table: firewall.cisco.asa
2025-07-03 13:19:17 - devo_backup - DEBUG - Analyzing table: network.switch.info
2025-07-03 13:19:17 - devo_backup - INFO - Progress: 10/28 tables analyzed
2025-07-03 13:19:17 - devo_backup - INFO - Executing Devo query: from network.switch.info select count() as record_count...
2025-07-03 13:19:17 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:17 - devo_backup - DEBUG - Executing query (attempt 1): from network.switch.info select count() as record_count...
2025-07-03 13:19:17 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:17 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:17 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:17 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:17 - devo_backup - INFO - Executing Devo query: from firewall.paloalto select * limit 5...
2025-07-03 13:19:17 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:17 - devo_backup - DEBUG - Executing query (attempt 1): from firewall.paloalto select * limit 5...
2025-07-03 13:19:17 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:17 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:18 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:18 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:18 - devo_backup - INFO - Executing Devo query: from my.app.data select * limit 5...
2025-07-03 13:19:18 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:18 - devo_backup - DEBUG - Executing query (attempt 1): from my.app.data select * limit 5...
2025-07-03 13:19:18 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:18 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:18 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:18 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:18 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:18 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:18 - devo_backup - INFO - Executing Devo query: from network.cisco.router select * limit 5...
2025-07-03 13:19:18 - devo_backup - INFO - Executing Devo query: from firewall.fortinet select * limit 3...
2025-07-03 13:19:18 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:18 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:18 - devo_backup - DEBUG - Executing query (attempt 1): from network.cisco.router select * limit 5...
2025-07-03 13:19:18 - devo_backup - DEBUG - Executing query (attempt 1): from firewall.fortinet select * limit 3...
2025-07-03 13:19:18 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:18 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:18 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:18 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:18 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:18 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:18 - devo_backup - INFO - Executing Devo query: from network.switch.info select * limit 5...
2025-07-03 13:19:18 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:18 - devo_backup - DEBUG - Executing query (attempt 1): from network.switch.info select * limit 5...
2025-07-03 13:19:18 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:18 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:18 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:18 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:18 - devo_backup - INFO - Executing Devo query: from firewall.paloalto select * limit 3...
2025-07-03 13:19:18 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:18 - devo_backup - DEBUG - Executing query (attempt 1): from firewall.paloalto select * limit 3...
2025-07-03 13:19:18 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:18 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:18 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:18 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:18 - devo_backup - INFO - Executing Devo query: from my.app.data select * limit 3...
2025-07-03 13:19:18 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:18 - devo_backup - DEBUG - Executing query (attempt 1): from my.app.data select * limit 3...
2025-07-03 13:19:18 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:18 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:18 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:18 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:18 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:18 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:18 - devo_backup - INFO - Executing Devo query: from firewall.fortinet select max(timestamp) as last_update...
2025-07-03 13:19:18 - devo_backup - INFO - Executing Devo query: from network.cisco.router select * limit 3...
2025-07-03 13:19:18 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:18 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:18 - devo_backup - DEBUG - Executing query (attempt 1): from firewall.fortinet select max(timestamp) as last_update...
2025-07-03 13:19:18 - devo_backup - DEBUG - Executing query (attempt 1): from network.cisco.router select * limit 3...
2025-07-03 13:19:18 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:18 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:18 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:18 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:18 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:18 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:18 - devo_backup - INFO - Executing Devo query: from network.switch.info select * limit 3...
2025-07-03 13:19:18 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:18 - devo_backup - DEBUG - Executing query (attempt 1): from network.switch.info select * limit 3...
2025-07-03 13:19:18 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:18 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:18 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:18 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:18 - devo_backup - INFO - Executing Devo query: from my.app.data select max(timestamp) as last_update...
2025-07-03 13:19:18 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:18 - devo_backup - DEBUG - Executing query (attempt 1): from my.app.data select max(timestamp) as last_update...
2025-07-03 13:19:18 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:18 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:18 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:18 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:18 - devo_backup - INFO - Executing Devo query: from firewall.paloalto select max(timestamp) as last_update...
2025-07-03 13:19:18 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:18 - devo_backup - DEBUG - Executing query (attempt 1): from firewall.paloalto select max(timestamp) as last_update...
2025-07-03 13:19:18 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:18 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:18 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:18 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:18 - devo_backup - INFO - Executing Devo query: from firewall.fortinet select max(eventdate) as last_update...
2025-07-03 13:19:18 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:18 - devo_backup - DEBUG - Executing query (attempt 1): from firewall.fortinet select max(eventdate) as last_update...
2025-07-03 13:19:18 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:18 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:18 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:18 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:18 - devo_backup - INFO - Executing Devo query: from network.cisco.router select max(timestamp) as last_update...
2025-07-03 13:19:18 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:18 - devo_backup - DEBUG - Executing query (attempt 1): from network.cisco.router select max(timestamp) as last_update...
2025-07-03 13:19:18 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:18 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:18 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:18 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:18 - devo_backup - INFO - Executing Devo query: from network.switch.info select max(timestamp) as last_update...
2025-07-03 13:19:18 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:18 - devo_backup - DEBUG - Executing query (attempt 1): from network.switch.info select max(timestamp) as last_update...
2025-07-03 13:19:18 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:18 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:18 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:18 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:18 - devo_backup - INFO - Executing Devo query: from my.app.data select max(eventdate) as last_update...
2025-07-03 13:19:18 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:18 - devo_backup - DEBUG - Executing query (attempt 1): from my.app.data select max(eventdate) as last_update...
2025-07-03 13:19:18 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:18 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:18 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:18 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:18 - devo_backup - INFO - Executing Devo query: from firewall.paloalto select max(eventdate) as last_update...
2025-07-03 13:19:18 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:18 - devo_backup - DEBUG - Executing query (attempt 1): from firewall.paloalto select max(eventdate) as last_update...
2025-07-03 13:19:18 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:18 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:18 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:18 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:18 - devo_backup - INFO - Executing Devo query: from firewall.fortinet select max(date) as last_update...
2025-07-03 13:19:18 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:18 - devo_backup - DEBUG - Executing query (attempt 1): from firewall.fortinet select max(date) as last_update...
2025-07-03 13:19:18 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:18 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:18 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:18 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:18 - devo_backup - INFO - Executing Devo query: from network.cisco.router select max(eventdate) as last_update...
2025-07-03 13:19:18 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:18 - devo_backup - DEBUG - Executing query (attempt 1): from network.cisco.router select max(eventdate) as last_update...
2025-07-03 13:19:18 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:18 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:18 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:18 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:18 - devo_backup - INFO - Executing Devo query: from network.switch.info select max(eventdate) as last_update...
2025-07-03 13:19:18 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:18 - devo_backup - DEBUG - Executing query (attempt 1): from network.switch.info select max(eventdate) as last_update...
2025-07-03 13:19:18 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:18 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:18 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:18 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:18 - devo_backup - INFO - Executing Devo query: from my.app.data select max(date) as last_update...
2025-07-03 13:19:18 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:18 - devo_backup - DEBUG - Executing query (attempt 1): from my.app.data select max(date) as last_update...
2025-07-03 13:19:18 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:18 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:18 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:18 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:18 - devo_backup - INFO - Executing Devo query: from firewall.fortinet select max(time) as last_update...
2025-07-03 13:19:18 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:18 - devo_backup - DEBUG - Executing query (attempt 1): from firewall.fortinet select max(time) as last_update...
2025-07-03 13:19:18 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:18 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:18 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:18 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:18 - devo_backup - INFO - Executing Devo query: from firewall.paloalto select max(date) as last_update...
2025-07-03 13:19:18 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:18 - devo_backup - DEBUG - Executing query (attempt 1): from firewall.paloalto select max(date) as last_update...
2025-07-03 13:19:18 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:18 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:18 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:18 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:18 - devo_backup - INFO - Executing Devo query: from network.cisco.router select max(date) as last_update...
2025-07-03 13:19:18 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:18 - devo_backup - DEBUG - Executing query (attempt 1): from network.cisco.router select max(date) as last_update...
2025-07-03 13:19:18 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:18 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:18 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:18 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:18 - devo_backup - INFO - Executing Devo query: from network.switch.info select max(date) as last_update...
2025-07-03 13:19:18 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:18 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:18 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:18 - devo_backup - DEBUG - Executing query (attempt 1): from network.switch.info select max(date) as last_update...
2025-07-03 13:19:18 - devo_backup - INFO - Executing Devo query: from my.app.data select max(time) as last_update...
2025-07-03 13:19:18 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:18 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:18 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:18 - devo_backup - DEBUG - Executing query (attempt 1): from my.app.data select max(time) as last_update...
2025-07-03 13:19:18 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:18 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:18 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:18 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:18 - devo_backup - INFO - Executing Devo query: from firewall.fortinet select max(_time) as last_update...
2025-07-03 13:19:18 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:18 - devo_backup - DEBUG - Executing query (attempt 1): from firewall.fortinet select max(_time) as last_update...
2025-07-03 13:19:18 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:18 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:18 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:18 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:18 - devo_backup - INFO - Executing Devo query: from firewall.paloalto select max(time) as last_update...
2025-07-03 13:19:18 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:18 - devo_backup - DEBUG - Executing query (attempt 1): from firewall.paloalto select max(time) as last_update...
2025-07-03 13:19:18 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:18 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:18 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:18 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:18 - devo_backup - INFO - Executing Devo query: from network.cisco.router select max(time) as last_update...
2025-07-03 13:19:18 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:18 - devo_backup - DEBUG - Executing query (attempt 1): from network.cisco.router select max(time) as last_update...
2025-07-03 13:19:18 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:18 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:18 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:18 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:18 - devo_backup - INFO - Executing Devo query: from my.app.data select max(_time) as last_update...
2025-07-03 13:19:18 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:18 - devo_backup - DEBUG - Executing query (attempt 1): from my.app.data select max(_time) as last_update...
2025-07-03 13:19:18 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:18 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:18 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:18 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:18 - devo_backup - INFO - Executing Devo query: from network.switch.info select max(time) as last_update...
2025-07-03 13:19:18 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:18 - devo_backup - DEBUG - Executing query (attempt 1): from network.switch.info select max(time) as last_update...
2025-07-03 13:19:18 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:18 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:18 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:18 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:18 - devo_backup - INFO - Executing Devo query: from firewall.fortinet select max(created_at) as last_update...
2025-07-03 13:19:18 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:18 - devo_backup - DEBUG - Executing query (attempt 1): from firewall.fortinet select max(created_at) as last_update...
2025-07-03 13:19:18 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:18 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:18 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:18 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:18 - devo_backup - INFO - Executing Devo query: from firewall.paloalto select max(_time) as last_update...
2025-07-03 13:19:18 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:18 - devo_backup - DEBUG - Executing query (attempt 1): from firewall.paloalto select max(_time) as last_update...
2025-07-03 13:19:18 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:18 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:18 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:18 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:18 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:18 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:18 - devo_backup - INFO - Executing Devo query: from network.cisco.router select max(_time) as last_update...
2025-07-03 13:19:18 - devo_backup - INFO - Executing Devo query: from my.app.data select max(created_at) as last_update...
2025-07-03 13:19:18 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:18 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:18 - devo_backup - DEBUG - Executing query (attempt 1): from network.cisco.router select max(_time) as last_update...
2025-07-03 13:19:18 - devo_backup - DEBUG - Executing query (attempt 1): from my.app.data select max(created_at) as last_update...
2025-07-03 13:19:18 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:18 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:18 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:18 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:18 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:18 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:18 - devo_backup - INFO - Executing Devo query: from network.switch.info select max(_time) as last_update...
2025-07-03 13:19:18 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:18 - devo_backup - DEBUG - Executing query (attempt 1): from network.switch.info select max(_time) as last_update...
2025-07-03 13:19:18 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:18 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:18 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:18 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:18 - devo_backup - INFO - Executing Devo query: from firewall.fortinet select max(updated_at) as last_update...
2025-07-03 13:19:18 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:18 - devo_backup - DEBUG - Executing query (attempt 1): from firewall.fortinet select max(updated_at) as last_update...
2025-07-03 13:19:18 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:18 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:18 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:18 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:18 - devo_backup - INFO - Executing Devo query: from firewall.paloalto select max(created_at) as last_update...
2025-07-03 13:19:18 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:18 - devo_backup - DEBUG - Executing query (attempt 1): from firewall.paloalto select max(created_at) as last_update...
2025-07-03 13:19:18 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:18 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:19 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:19 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:19 - devo_backup - INFO - Executing Devo query: from my.app.data select max(updated_at) as last_update...
2025-07-03 13:19:19 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:19 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:19 - devo_backup - DEBUG - Executing query (attempt 1): from my.app.data select max(updated_at) as last_update...
2025-07-03 13:19:19 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:19 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:19 - devo_backup - INFO - Executing Devo query: from network.cisco.router select max(created_at) as last_update...
2025-07-03 13:19:19 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:19 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:19 - devo_backup - DEBUG - Executing query (attempt 1): from network.cisco.router select max(created_at) as last_update...
2025-07-03 13:19:19 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:19 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:19 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:19 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:19 - devo_backup - INFO - Executing Devo query: from network.switch.info select max(created_at) as last_update...
2025-07-03 13:19:19 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:19 - devo_backup - DEBUG - Executing query (attempt 1): from network.switch.info select max(created_at) as last_update...
2025-07-03 13:19:19 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:19 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:19 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:19 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:19 - devo_backup - DEBUG - Successfully analyzed table: firewall.fortinet
2025-07-03 13:19:19 - devo_backup - DEBUG - Analyzing table: siem.logtrust.alert.info
2025-07-03 13:19:19 - devo_backup - INFO - Executing Devo query: from siem.logtrust.alert.info select count() as record_count...
2025-07-03 13:19:19 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:19 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.alert.info select count() as record_count...
2025-07-03 13:19:19 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:19 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:19 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:19 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:19 - devo_backup - INFO - Executing Devo query: from firewall.paloalto select max(updated_at) as last_update...
2025-07-03 13:19:19 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:19 - devo_backup - DEBUG - Executing query (attempt 1): from firewall.paloalto select max(updated_at) as last_update...
2025-07-03 13:19:19 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:19 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:19 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:19 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:19 - devo_backup - DEBUG - Successfully analyzed table: my.app.data
2025-07-03 13:19:19 - devo_backup - DEBUG - Analyzing table: siem.logtrust.auth.info
2025-07-03 13:19:19 - devo_backup - INFO - Executing Devo query: from siem.logtrust.auth.info select count() as record_count...
2025-07-03 13:19:19 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:19 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.auth.info select count() as record_count...
2025-07-03 13:19:19 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:19 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:19 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:19 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:19 - devo_backup - INFO - Executing Devo query: from network.switch.info select max(updated_at) as last_update...
2025-07-03 13:19:19 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:19 - devo_backup - DEBUG - Executing query (attempt 1): from network.switch.info select max(updated_at) as last_update...
2025-07-03 13:19:19 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:19 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:19 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:19 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:19 - devo_backup - INFO - Executing Devo query: from network.cisco.router select max(updated_at) as last_update...
2025-07-03 13:19:19 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:19 - devo_backup - DEBUG - Executing query (attempt 1): from network.cisco.router select max(updated_at) as last_update...
2025-07-03 13:19:19 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:19 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:19 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:19 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:19 - devo_backup - INFO - Executing Devo query: from siem.logtrust.alert.info select * limit 5...
2025-07-03 13:19:19 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:19 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.alert.info select * limit 5...
2025-07-03 13:19:19 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:19 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:19 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:19 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:19 - devo_backup - DEBUG - Successfully analyzed table: firewall.paloalto
2025-07-03 13:19:19 - devo_backup - DEBUG - Analyzing table: siem.logtrust.dns.info
2025-07-03 13:19:19 - devo_backup - INFO - Executing Devo query: from siem.logtrust.dns.info select count() as record_count...
2025-07-03 13:19:19 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:19 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.dns.info select count() as record_count...
2025-07-03 13:19:19 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:19 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:19 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:19 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:19 - devo_backup - INFO - Executing Devo query: from siem.logtrust.auth.info select * limit 5...
2025-07-03 13:19:19 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:19 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.auth.info select * limit 5...
2025-07-03 13:19:19 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:19 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:19 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:19 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:19 - devo_backup - DEBUG - Successfully analyzed table: network.switch.info
2025-07-03 13:19:19 - devo_backup - DEBUG - Analyzing table: siem.logtrust.file.info
2025-07-03 13:19:19 - devo_backup - INFO - Executing Devo query: from siem.logtrust.file.info select count() as record_count...
2025-07-03 13:19:19 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:19 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.file.info select count() as record_count...
2025-07-03 13:19:19 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:19 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:19 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:19 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:19 - devo_backup - DEBUG - Successfully analyzed table: network.cisco.router
2025-07-03 13:19:19 - devo_backup - DEBUG - Analyzing table: siem.logtrust.network.info
2025-07-03 13:19:19 - devo_backup - INFO - Executing Devo query: from siem.logtrust.network.info select count() as record_count...
2025-07-03 13:19:19 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:19 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.network.info select count() as record_count...
2025-07-03 13:19:19 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:19 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:19 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:19 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:19 - devo_backup - INFO - Executing Devo query: from siem.logtrust.alert.info select * limit 3...
2025-07-03 13:19:19 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:19 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.alert.info select * limit 3...
2025-07-03 13:19:19 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:19 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:19 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:19 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:19 - devo_backup - INFO - Executing Devo query: from siem.logtrust.dns.info select * limit 5...
2025-07-03 13:19:19 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:19 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.dns.info select * limit 5...
2025-07-03 13:19:19 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:19 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:19 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:19 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:19 - devo_backup - INFO - Executing Devo query: from siem.logtrust.auth.info select * limit 3...
2025-07-03 13:19:19 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:19 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.auth.info select * limit 3...
2025-07-03 13:19:19 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:19 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:19 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:19 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:19 - devo_backup - INFO - Executing Devo query: from siem.logtrust.file.info select * limit 5...
2025-07-03 13:19:19 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:19 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.file.info select * limit 5...
2025-07-03 13:19:19 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:19 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:19 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:19 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:19 - devo_backup - INFO - Executing Devo query: from siem.logtrust.network.info select * limit 5...
2025-07-03 13:19:19 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:19 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.network.info select * limit 5...
2025-07-03 13:19:19 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:19 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:19 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:19 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:19 - devo_backup - INFO - Executing Devo query: from siem.logtrust.alert.info select max(timestamp) as last_update...
2025-07-03 13:19:19 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:19 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.alert.info select max(timestamp) as last_update...
2025-07-03 13:19:19 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:19 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:19 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:19 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:19 - devo_backup - INFO - Executing Devo query: from siem.logtrust.auth.info select max(timestamp) as last_update...
2025-07-03 13:19:19 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:19 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.auth.info select max(timestamp) as last_update...
2025-07-03 13:19:19 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:19 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:19 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:19 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:19 - devo_backup - INFO - Executing Devo query: from siem.logtrust.dns.info select * limit 3...
2025-07-03 13:19:19 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:19 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.dns.info select * limit 3...
2025-07-03 13:19:19 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:19 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:19 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:19 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:19 - devo_backup - INFO - Executing Devo query: from siem.logtrust.file.info select * limit 3...
2025-07-03 13:19:19 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:19 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.file.info select * limit 3...
2025-07-03 13:19:19 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:19 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:19 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:19 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:19 - devo_backup - INFO - Executing Devo query: from siem.logtrust.network.info select * limit 3...
2025-07-03 13:19:19 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:19 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.network.info select * limit 3...
2025-07-03 13:19:19 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:19 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:19 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:19 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:19 - devo_backup - INFO - Executing Devo query: from siem.logtrust.alert.info select max(eventdate) as last_update...
2025-07-03 13:19:19 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:19 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.alert.info select max(eventdate) as last_update...
2025-07-03 13:19:19 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:19 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:19 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:19 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:19 - devo_backup - INFO - Executing Devo query: from siem.logtrust.dns.info select max(timestamp) as last_update...
2025-07-03 13:19:19 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:19 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:19 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.dns.info select max(timestamp) as last_update...
2025-07-03 13:19:19 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:19 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:19 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:19 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:19 - devo_backup - INFO - Executing Devo query: from siem.logtrust.auth.info select max(eventdate) as last_update...
2025-07-03 13:19:19 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:19 - devo_backup - INFO - Executing Devo query: from siem.logtrust.file.info select max(timestamp) as last_update...
2025-07-03 13:19:19 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:19 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:19 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.auth.info select max(eventdate) as last_update...
2025-07-03 13:19:19 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.file.info select max(timestamp) as last_update...
2025-07-03 13:19:19 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:19 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:19 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:19 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:19 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:19 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:19 - devo_backup - INFO - Executing Devo query: from siem.logtrust.network.info select max(timestamp) as last_update...
2025-07-03 13:19:19 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:19 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.network.info select max(timestamp) as last_update...
2025-07-03 13:19:19 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:19 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:19 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:19 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:19 - devo_backup - INFO - Executing Devo query: from siem.logtrust.alert.info select max(date) as last_update...
2025-07-03 13:19:19 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:19 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:19 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.alert.info select max(date) as last_update...
2025-07-03 13:19:19 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:19 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:19 - devo_backup - INFO - Executing Devo query: from siem.logtrust.dns.info select max(eventdate) as last_update...
2025-07-03 13:19:19 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:19 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:19 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:19 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.dns.info select max(eventdate) as last_update...
2025-07-03 13:19:19 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:19 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:19 - devo_backup - INFO - Executing Devo query: from siem.logtrust.auth.info select max(date) as last_update...
2025-07-03 13:19:19 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:19 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:19 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.auth.info select max(date) as last_update...
2025-07-03 13:19:19 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:19 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:19 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:19 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:19 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:19 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:19 - devo_backup - INFO - Executing Devo query: from siem.logtrust.network.info select max(eventdate) as last_update...
2025-07-03 13:19:19 - devo_backup - INFO - Executing Devo query: from siem.logtrust.file.info select max(eventdate) as last_update...
2025-07-03 13:19:19 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:19 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:19 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.network.info select max(eventdate) as last_update...
2025-07-03 13:19:19 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.file.info select max(eventdate) as last_update...
2025-07-03 13:19:19 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:19 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:19 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:19 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:19 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:19 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:19 - devo_backup - INFO - Executing Devo query: from siem.logtrust.dns.info select max(date) as last_update...
2025-07-03 13:19:19 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:19 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.dns.info select max(date) as last_update...
2025-07-03 13:19:19 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:19 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:20 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:20 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:20 - devo_backup - INFO - Executing Devo query: from siem.logtrust.auth.info select max(time) as last_update...
2025-07-03 13:19:20 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:20 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.auth.info select max(time) as last_update...
2025-07-03 13:19:20 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:20 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:20 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:20 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:20 - devo_backup - INFO - Executing Devo query: from siem.logtrust.file.info select max(date) as last_update...
2025-07-03 13:19:20 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:20 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.file.info select max(date) as last_update...
2025-07-03 13:19:20 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:20 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:20 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:20 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:20 - devo_backup - INFO - Executing Devo query: from siem.logtrust.network.info select max(date) as last_update...
2025-07-03 13:19:20 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:20 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.network.info select max(date) as last_update...
2025-07-03 13:19:20 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:20 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:20 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:20 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:20 - devo_backup - INFO - Executing Devo query: from siem.logtrust.alert.info select max(time) as last_update...
2025-07-03 13:19:20 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:20 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.alert.info select max(time) as last_update...
2025-07-03 13:19:20 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:20 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:20 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:20 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:20 - devo_backup - INFO - Executing Devo query: from siem.logtrust.dns.info select max(time) as last_update...
2025-07-03 13:19:20 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:20 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.dns.info select max(time) as last_update...
2025-07-03 13:19:20 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:20 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:20 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:20 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:20 - devo_backup - INFO - Executing Devo query: from siem.logtrust.auth.info select max(_time) as last_update...
2025-07-03 13:19:20 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:20 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.auth.info select max(_time) as last_update...
2025-07-03 13:19:20 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:20 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:20 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:20 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:20 - devo_backup - INFO - Executing Devo query: from siem.logtrust.network.info select max(time) as last_update...
2025-07-03 13:19:20 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:20 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.network.info select max(time) as last_update...
2025-07-03 13:19:20 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:20 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:20 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:20 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:20 - devo_backup - INFO - Executing Devo query: from siem.logtrust.file.info select max(time) as last_update...
2025-07-03 13:19:20 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:20 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.file.info select max(time) as last_update...
2025-07-03 13:19:20 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:20 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:20 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:20 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:20 - devo_backup - INFO - Executing Devo query: from siem.logtrust.alert.info select max(_time) as last_update...
2025-07-03 13:19:20 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:20 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.alert.info select max(_time) as last_update...
2025-07-03 13:19:20 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:20 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:20 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:20 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:20 - devo_backup - INFO - Executing Devo query: from siem.logtrust.dns.info select max(_time) as last_update...
2025-07-03 13:19:20 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:20 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.dns.info select max(_time) as last_update...
2025-07-03 13:19:20 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:20 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:20 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:20 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:20 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:20 - devo_backup - INFO - Executing Devo query: from siem.logtrust.network.info select max(_time) as last_update...
2025-07-03 13:19:20 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:20 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:20 - devo_backup - INFO - Executing Devo query: from siem.logtrust.auth.info select max(created_at) as last_update...
2025-07-03 13:19:20 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.network.info select max(_time) as last_update...
2025-07-03 13:19:20 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:20 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:20 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.auth.info select max(created_at) as last_update...
2025-07-03 13:19:20 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:20 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:20 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:20 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:20 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:20 - devo_backup - INFO - Executing Devo query: from siem.logtrust.file.info select max(_time) as last_update...
2025-07-03 13:19:20 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:20 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.file.info select max(_time) as last_update...
2025-07-03 13:19:20 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:20 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:20 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:20 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:20 - devo_backup - INFO - Executing Devo query: from siem.logtrust.alert.info select max(created_at) as last_update...
2025-07-03 13:19:20 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:20 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.alert.info select max(created_at) as last_update...
2025-07-03 13:19:20 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:20 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:20 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:20 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:20 - devo_backup - INFO - Executing Devo query: from siem.logtrust.dns.info select max(created_at) as last_update...
2025-07-03 13:19:20 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:20 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.dns.info select max(created_at) as last_update...
2025-07-03 13:19:20 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:20 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:20 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:20 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:20 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:20 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:20 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:20 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:20 - devo_backup - INFO - Executing Devo query: from siem.logtrust.auth.info select max(updated_at) as last_update...
2025-07-03 13:19:20 - devo_backup - INFO - Executing Devo query: from siem.logtrust.file.info select max(created_at) as last_update...
2025-07-03 13:19:20 - devo_backup - INFO - Executing Devo query: from siem.logtrust.network.info select max(created_at) as last_update...
2025-07-03 13:19:20 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:20 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:20 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:20 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.auth.info select max(updated_at) as last_update...
2025-07-03 13:19:20 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.file.info select max(created_at) as last_update...
2025-07-03 13:19:20 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.network.info select max(created_at) as last_update...
2025-07-03 13:19:20 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:20 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:20 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:20 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:20 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:20 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:20 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:20 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:20 - devo_backup - INFO - Executing Devo query: from siem.logtrust.alert.info select max(updated_at) as last_update...
2025-07-03 13:19:20 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:20 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.alert.info select max(updated_at) as last_update...
2025-07-03 13:19:20 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:20 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:20 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:20 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:20 - devo_backup - INFO - Executing Devo query: from siem.logtrust.dns.info select max(updated_at) as last_update...
2025-07-03 13:19:20 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:20 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.dns.info select max(updated_at) as last_update...
2025-07-03 13:19:20 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:20 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:20 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:20 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:20 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:20 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:20 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:20 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:20 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:20 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:20 - devo_backup - DEBUG - Successfully analyzed table: siem.logtrust.alert.info
2025-07-03 13:19:20 - devo_backup - INFO - Executing Devo query: from siem.logtrust.network.info select max(updated_at) as last_update...
2025-07-03 13:19:20 - devo_backup - INFO - Executing Devo query: from siem.logtrust.file.info select max(updated_at) as last_update...
2025-07-03 13:19:20 - devo_backup - DEBUG - Successfully analyzed table: siem.logtrust.auth.info
2025-07-03 13:19:20 - devo_backup - DEBUG - Analyzing table: siem.logtrust.process.info
2025-07-03 13:19:20 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:20 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:20 - devo_backup - DEBUG - Analyzing table: siem.logtrust.registry.info
2025-07-03 13:19:20 - devo_backup - INFO - Executing Devo query: from siem.logtrust.process.info select count() as record_count...
2025-07-03 13:19:20 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.network.info select max(updated_at) as last_update...
2025-07-03 13:19:20 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.file.info select max(updated_at) as last_update...
2025-07-03 13:19:20 - devo_backup - INFO - Executing Devo query: from siem.logtrust.registry.info select count() as record_count...
2025-07-03 13:19:20 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:20 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:20 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:20 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:20 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.process.info select count() as record_count...
2025-07-03 13:19:20 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:20 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:20 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.registry.info select count() as record_count...
2025-07-03 13:19:20 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:20 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:20 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:20 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:20 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:20 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:20 - devo_backup - DEBUG - Successfully analyzed table: siem.logtrust.dns.info
2025-07-03 13:19:20 - devo_backup - DEBUG - Analyzing table: siem.logtrust.system.info
2025-07-03 13:19:20 - devo_backup - INFO - Executing Devo query: from siem.logtrust.system.info select count() as record_count...
2025-07-03 13:19:20 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:20 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.system.info select count() as record_count...
2025-07-03 13:19:20 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:20 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:20 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:20 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:20 - devo_backup - DEBUG - Successfully analyzed table: siem.logtrust.network.info
2025-07-03 13:19:20 - devo_backup - DEBUG - Analyzing table: siem.logtrust.web.activity
2025-07-03 13:19:20 - devo_backup - INFO - Executing Devo query: from siem.logtrust.web.activity select count() as record_count...
2025-07-03 13:19:20 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:20 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.web.activity select count() as record_count...
2025-07-03 13:19:20 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:20 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:20 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:20 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:20 - devo_backup - DEBUG - Successfully analyzed table: siem.logtrust.file.info
2025-07-03 13:19:20 - devo_backup - DEBUG - Analyzing table: system.auth.login
2025-07-03 13:19:20 - devo_backup - INFO - Progress: 20/28 tables analyzed
2025-07-03 13:19:20 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:20 - devo_backup - INFO - Executing Devo query: from system.auth.login select count() as record_count...
2025-07-03 13:19:20 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:20 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:20 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:20 - devo_backup - INFO - Executing Devo query: from siem.logtrust.registry.info select * limit 5...
2025-07-03 13:19:20 - devo_backup - DEBUG - Executing query (attempt 1): from system.auth.login select count() as record_count...
2025-07-03 13:19:20 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:20 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:20 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:20 - devo_backup - INFO - Executing Devo query: from siem.logtrust.system.info select * limit 5...
2025-07-03 13:19:20 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.registry.info select * limit 5...
2025-07-03 13:19:20 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:20 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:20 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:20 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:20 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:20 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.system.info select * limit 5...
2025-07-03 13:19:20 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:20 - devo_backup - INFO - Executing Devo query: from siem.logtrust.process.info select * limit 5...
2025-07-03 13:19:20 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:20 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:20 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:20 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.process.info select * limit 5...
2025-07-03 13:19:20 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:20 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:20 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:20 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:20 - devo_backup - INFO - Executing Devo query: from siem.logtrust.web.activity select * limit 5...
2025-07-03 13:19:20 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:20 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.web.activity select * limit 5...
2025-07-03 13:19:20 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:20 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:20 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:20 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:20 - devo_backup - INFO - Executing Devo query: from system.auth.login select * limit 5...
2025-07-03 13:19:20 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:20 - devo_backup - DEBUG - Executing query (attempt 1): from system.auth.login select * limit 5...
2025-07-03 13:19:20 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:20 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:20 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:20 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:20 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:20 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:20 - devo_backup - INFO - Executing Devo query: from siem.logtrust.process.info select * limit 3...
2025-07-03 13:19:20 - devo_backup - INFO - Executing Devo query: from siem.logtrust.system.info select * limit 3...
2025-07-03 13:19:20 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:20 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:20 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.process.info select * limit 3...
2025-07-03 13:19:20 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.system.info select * limit 3...
2025-07-03 13:19:20 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:20 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:20 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:20 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:20 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:20 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:20 - devo_backup - INFO - Executing Devo query: from siem.logtrust.registry.info select * limit 3...
2025-07-03 13:19:20 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:20 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.registry.info select * limit 3...
2025-07-03 13:19:20 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:20 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:21 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:21 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:21 - devo_backup - INFO - Executing Devo query: from system.auth.login select * limit 3...
2025-07-03 13:19:21 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:21 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:21 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:21 - devo_backup - DEBUG - Executing query (attempt 1): from system.auth.login select * limit 3...
2025-07-03 13:19:21 - devo_backup - INFO - Executing Devo query: from siem.logtrust.web.activity select * limit 3...
2025-07-03 13:19:21 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:21 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:21 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:21 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.web.activity select * limit 3...
2025-07-03 13:19:21 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:21 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:21 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:21 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:21 - devo_backup - INFO - Executing Devo query: from siem.logtrust.system.info select max(timestamp) as last_update...
2025-07-03 13:19:21 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:21 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.system.info select max(timestamp) as last_update...
2025-07-03 13:19:21 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:21 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:21 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:21 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:21 - devo_backup - INFO - Executing Devo query: from siem.logtrust.process.info select max(timestamp) as last_update...
2025-07-03 13:19:21 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:21 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.process.info select max(timestamp) as last_update...
2025-07-03 13:19:21 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:21 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:21 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:21 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:21 - devo_backup - INFO - Executing Devo query: from siem.logtrust.registry.info select max(timestamp) as last_update...
2025-07-03 13:19:21 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:21 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.registry.info select max(timestamp) as last_update...
2025-07-03 13:19:21 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:21 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:21 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:21 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:21 - devo_backup - INFO - Executing Devo query: from system.auth.login select max(timestamp) as last_update...
2025-07-03 13:19:21 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:21 - devo_backup - DEBUG - Executing query (attempt 1): from system.auth.login select max(timestamp) as last_update...
2025-07-03 13:19:21 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:21 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:21 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:21 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:21 - devo_backup - INFO - Executing Devo query: from siem.logtrust.web.activity select max(timestamp) as last_update...
2025-07-03 13:19:21 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:21 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.web.activity select max(timestamp) as last_update...
2025-07-03 13:19:21 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:21 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:21 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:21 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:21 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:21 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:21 - devo_backup - INFO - Executing Devo query: from siem.logtrust.process.info select max(eventdate) as last_update...
2025-07-03 13:19:21 - devo_backup - INFO - Executing Devo query: from siem.logtrust.system.info select max(eventdate) as last_update...
2025-07-03 13:19:21 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:21 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:21 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.process.info select max(eventdate) as last_update...
2025-07-03 13:19:21 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.system.info select max(eventdate) as last_update...
2025-07-03 13:19:21 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:21 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:21 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:21 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:21 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:21 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:21 - devo_backup - INFO - Executing Devo query: from siem.logtrust.registry.info select max(eventdate) as last_update...
2025-07-03 13:19:21 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:21 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.registry.info select max(eventdate) as last_update...
2025-07-03 13:19:21 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:21 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:21 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:21 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:21 - devo_backup - INFO - Executing Devo query: from siem.logtrust.web.activity select max(eventdate) as last_update...
2025-07-03 13:19:21 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:21 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.web.activity select max(eventdate) as last_update...
2025-07-03 13:19:21 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:21 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:21 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:21 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:21 - devo_backup - INFO - Executing Devo query: from system.auth.login select max(eventdate) as last_update...
2025-07-03 13:19:21 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:21 - devo_backup - DEBUG - Executing query (attempt 1): from system.auth.login select max(eventdate) as last_update...
2025-07-03 13:19:21 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:21 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:21 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:21 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:21 - devo_backup - INFO - Executing Devo query: from siem.logtrust.process.info select max(date) as last_update...
2025-07-03 13:19:21 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:21 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.process.info select max(date) as last_update...
2025-07-03 13:19:21 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:21 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:21 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:21 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:21 - devo_backup - INFO - Executing Devo query: from siem.logtrust.system.info select max(date) as last_update...
2025-07-03 13:19:21 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:21 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.system.info select max(date) as last_update...
2025-07-03 13:19:21 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:21 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:21 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:21 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:21 - devo_backup - INFO - Executing Devo query: from siem.logtrust.registry.info select max(date) as last_update...
2025-07-03 13:19:21 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:21 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.registry.info select max(date) as last_update...
2025-07-03 13:19:21 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:21 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:21 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:21 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:21 - devo_backup - INFO - Executing Devo query: from siem.logtrust.web.activity select max(date) as last_update...
2025-07-03 13:19:21 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:21 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.web.activity select max(date) as last_update...
2025-07-03 13:19:21 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:21 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:21 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:21 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:21 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:21 - devo_backup - INFO - Executing Devo query: from siem.logtrust.process.info select max(time) as last_update...
2025-07-03 13:19:21 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:21 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:21 - devo_backup - INFO - Executing Devo query: from system.auth.login select max(date) as last_update...
2025-07-03 13:19:21 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.process.info select max(time) as last_update...
2025-07-03 13:19:21 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:21 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:21 - devo_backup - DEBUG - Executing query (attempt 1): from system.auth.login select max(date) as last_update...
2025-07-03 13:19:21 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:21 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:21 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:21 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:21 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:21 - devo_backup - INFO - Executing Devo query: from siem.logtrust.system.info select max(time) as last_update...
2025-07-03 13:19:21 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:21 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.system.info select max(time) as last_update...
2025-07-03 13:19:21 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:21 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:21 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:21 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:21 - devo_backup - INFO - Executing Devo query: from siem.logtrust.registry.info select max(time) as last_update...
2025-07-03 13:19:21 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:21 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.registry.info select max(time) as last_update...
2025-07-03 13:19:21 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:21 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:21 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:21 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:21 - devo_backup - INFO - Executing Devo query: from siem.logtrust.web.activity select max(time) as last_update...
2025-07-03 13:19:21 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:21 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.web.activity select max(time) as last_update...
2025-07-03 13:19:21 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:21 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:21 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:21 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:21 - devo_backup - INFO - Executing Devo query: from siem.logtrust.system.info select max(_time) as last_update...
2025-07-03 13:19:21 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:21 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:21 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:21 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:21 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.system.info select max(_time) as last_update...
2025-07-03 13:19:21 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:21 - devo_backup - INFO - Executing Devo query: from system.auth.login select max(time) as last_update...
2025-07-03 13:19:21 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:21 - devo_backup - INFO - Executing Devo query: from siem.logtrust.process.info select max(_time) as last_update...
2025-07-03 13:19:21 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:21 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:21 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:21 - devo_backup - DEBUG - Executing query (attempt 1): from system.auth.login select max(time) as last_update...
2025-07-03 13:19:21 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.process.info select max(_time) as last_update...
2025-07-03 13:19:21 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:21 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:21 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:21 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:21 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:21 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:21 - devo_backup - INFO - Executing Devo query: from siem.logtrust.registry.info select max(_time) as last_update...
2025-07-03 13:19:21 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:21 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.registry.info select max(_time) as last_update...
2025-07-03 13:19:21 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:21 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:21 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:21 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:21 - devo_backup - INFO - Executing Devo query: from siem.logtrust.web.activity select max(_time) as last_update...
2025-07-03 13:19:21 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:21 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.web.activity select max(_time) as last_update...
2025-07-03 13:19:21 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:21 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:21 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:21 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:21 - devo_backup - INFO - Executing Devo query: from siem.logtrust.system.info select max(created_at) as last_update...
2025-07-03 13:19:21 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:21 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.system.info select max(created_at) as last_update...
2025-07-03 13:19:21 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:21 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:21 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:21 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:21 - devo_backup - INFO - Executing Devo query: from system.auth.login select max(_time) as last_update...
2025-07-03 13:19:21 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:21 - devo_backup - DEBUG - Executing query (attempt 1): from system.auth.login select max(_time) as last_update...
2025-07-03 13:19:21 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:21 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:21 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:21 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:21 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:21 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:21 - devo_backup - INFO - Executing Devo query: from siem.logtrust.registry.info select max(created_at) as last_update...
2025-07-03 13:19:21 - devo_backup - INFO - Executing Devo query: from siem.logtrust.process.info select max(created_at) as last_update...
2025-07-03 13:19:21 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:21 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:21 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.registry.info select max(created_at) as last_update...
2025-07-03 13:19:21 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.process.info select max(created_at) as last_update...
2025-07-03 13:19:21 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:21 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:21 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:21 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:21 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:21 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:21 - devo_backup - INFO - Executing Devo query: from siem.logtrust.web.activity select max(created_at) as last_update...
2025-07-03 13:19:21 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:21 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.web.activity select max(created_at) as last_update...
2025-07-03 13:19:21 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:21 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:21 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:21 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:21 - devo_backup - INFO - Executing Devo query: from siem.logtrust.system.info select max(updated_at) as last_update...
2025-07-03 13:19:21 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:21 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.system.info select max(updated_at) as last_update...
2025-07-03 13:19:21 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:21 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:21 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:21 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:21 - devo_backup - INFO - Executing Devo query: from system.auth.login select max(created_at) as last_update...
2025-07-03 13:19:21 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:21 - devo_backup - DEBUG - Executing query (attempt 1): from system.auth.login select max(created_at) as last_update...
2025-07-03 13:19:21 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:21 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:21 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:21 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:21 - devo_backup - INFO - Executing Devo query: from siem.logtrust.process.info select max(updated_at) as last_update...
2025-07-03 13:19:21 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:21 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.process.info select max(updated_at) as last_update...
2025-07-03 13:19:21 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:21 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:21 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:21 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:21 - devo_backup - INFO - Executing Devo query: from siem.logtrust.registry.info select max(updated_at) as last_update...
2025-07-03 13:19:21 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:21 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.registry.info select max(updated_at) as last_update...
2025-07-03 13:19:21 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:21 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:22 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:22 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:22 - devo_backup - INFO - Executing Devo query: from siem.logtrust.web.activity select max(updated_at) as last_update...
2025-07-03 13:19:22 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:22 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.web.activity select max(updated_at) as last_update...
2025-07-03 13:19:22 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:22 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:22 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:22 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:22 - devo_backup - DEBUG - Successfully analyzed table: siem.logtrust.system.info
2025-07-03 13:19:22 - devo_backup - DEBUG - Analyzing table: system.linux.syslog
2025-07-03 13:19:22 - devo_backup - INFO - Executing Devo query: from system.linux.syslog select count() as record_count...
2025-07-03 13:19:22 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:22 - devo_backup - DEBUG - Executing query (attempt 1): from system.linux.syslog select count() as record_count...
2025-07-03 13:19:22 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:22 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:22 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:22 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:22 - devo_backup - INFO - Executing Devo query: from system.auth.login select max(updated_at) as last_update...
2025-07-03 13:19:22 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:22 - devo_backup - DEBUG - Executing query (attempt 1): from system.auth.login select max(updated_at) as last_update...
2025-07-03 13:19:22 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:22 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:22 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:22 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:22 - devo_backup - DEBUG - Successfully analyzed table: siem.logtrust.registry.info
2025-07-03 13:19:22 - devo_backup - DEBUG - Analyzing table: system.windows.eventlog
2025-07-03 13:19:22 - devo_backup - INFO - Executing Devo query: from system.windows.eventlog select count() as record_count...
2025-07-03 13:19:22 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:22 - devo_backup - DEBUG - Executing query (attempt 1): from system.windows.eventlog select count() as record_count...
2025-07-03 13:19:22 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:22 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:22 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:22 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:22 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:22 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:22 - devo_backup - DEBUG - Successfully analyzed table: siem.logtrust.process.info
2025-07-03 13:19:22 - devo_backup - DEBUG - Successfully analyzed table: siem.logtrust.web.activity
2025-07-03 13:19:22 - devo_backup - DEBUG - Analyzing table: user.activity.log
2025-07-03 13:19:22 - devo_backup - INFO - Executing Devo query: from user.activity.log select count() as record_count...
2025-07-03 13:19:22 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:22 - devo_backup - DEBUG - Executing query (attempt 1): from user.activity.log select count() as record_count...
2025-07-03 13:19:22 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:22 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:22 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:22 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:22 - devo_backup - DEBUG - Successfully analyzed table: system.auth.login
2025-07-03 13:19:22 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:22 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:22 - devo_backup - INFO - Executing Devo query: from system.linux.syslog select * limit 5...
2025-07-03 13:19:22 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:22 - devo_backup - DEBUG - Executing query (attempt 1): from system.linux.syslog select * limit 5...
2025-07-03 13:19:22 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:22 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:22 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:22 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:22 - devo_backup - INFO - Executing Devo query: from system.windows.eventlog select * limit 5...
2025-07-03 13:19:22 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:22 - devo_backup - DEBUG - Executing query (attempt 1): from system.windows.eventlog select * limit 5...
2025-07-03 13:19:22 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:22 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:22 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:22 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:22 - devo_backup - INFO - Executing Devo query: from user.activity.log select * limit 5...
2025-07-03 13:19:22 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:22 - devo_backup - DEBUG - Executing query (attempt 1): from user.activity.log select * limit 5...
2025-07-03 13:19:22 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:22 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:22 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:22 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:22 - devo_backup - INFO - Executing Devo query: from system.linux.syslog select * limit 3...
2025-07-03 13:19:22 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:22 - devo_backup - DEBUG - Executing query (attempt 1): from system.linux.syslog select * limit 3...
2025-07-03 13:19:22 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:22 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:22 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:22 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:22 - devo_backup - INFO - Executing Devo query: from system.windows.eventlog select * limit 3...
2025-07-03 13:19:22 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:22 - devo_backup - DEBUG - Executing query (attempt 1): from system.windows.eventlog select * limit 3...
2025-07-03 13:19:22 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:22 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:22 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:22 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:22 - devo_backup - INFO - Executing Devo query: from user.activity.log select * limit 3...
2025-07-03 13:19:22 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:22 - devo_backup - DEBUG - Executing query (attempt 1): from user.activity.log select * limit 3...
2025-07-03 13:19:22 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:22 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:22 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:22 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:22 - devo_backup - INFO - Executing Devo query: from system.linux.syslog select max(timestamp) as last_update...
2025-07-03 13:19:22 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:22 - devo_backup - DEBUG - Executing query (attempt 1): from system.linux.syslog select max(timestamp) as last_update...
2025-07-03 13:19:22 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:22 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:22 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:22 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:22 - devo_backup - INFO - Executing Devo query: from system.windows.eventlog select max(timestamp) as last_update...
2025-07-03 13:19:22 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:22 - devo_backup - DEBUG - Executing query (attempt 1): from system.windows.eventlog select max(timestamp) as last_update...
2025-07-03 13:19:22 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:22 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:22 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:22 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:22 - devo_backup - INFO - Executing Devo query: from user.activity.log select max(timestamp) as last_update...
2025-07-03 13:19:22 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:22 - devo_backup - DEBUG - Executing query (attempt 1): from user.activity.log select max(timestamp) as last_update...
2025-07-03 13:19:22 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:22 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:22 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:22 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:22 - devo_backup - INFO - Executing Devo query: from system.linux.syslog select max(eventdate) as last_update...
2025-07-03 13:19:22 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:22 - devo_backup - DEBUG - Executing query (attempt 1): from system.linux.syslog select max(eventdate) as last_update...
2025-07-03 13:19:22 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:22 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:22 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:22 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:22 - devo_backup - INFO - Executing Devo query: from system.windows.eventlog select max(eventdate) as last_update...
2025-07-03 13:19:22 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:22 - devo_backup - DEBUG - Executing query (attempt 1): from system.windows.eventlog select max(eventdate) as last_update...
2025-07-03 13:19:22 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:22 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:22 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:22 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:22 - devo_backup - INFO - Executing Devo query: from user.activity.log select max(eventdate) as last_update...
2025-07-03 13:19:22 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:22 - devo_backup - DEBUG - Executing query (attempt 1): from user.activity.log select max(eventdate) as last_update...
2025-07-03 13:19:22 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:22 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:22 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:22 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:22 - devo_backup - INFO - Executing Devo query: from system.windows.eventlog select max(date) as last_update...
2025-07-03 13:19:22 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:22 - devo_backup - DEBUG - Executing query (attempt 1): from system.windows.eventlog select max(date) as last_update...
2025-07-03 13:19:22 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:22 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:22 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:22 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:22 - devo_backup - INFO - Executing Devo query: from user.activity.log select max(date) as last_update...
2025-07-03 13:19:22 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:22 - devo_backup - DEBUG - Executing query (attempt 1): from user.activity.log select max(date) as last_update...
2025-07-03 13:19:22 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:22 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:22 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:22 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:22 - devo_backup - INFO - Executing Devo query: from system.windows.eventlog select max(time) as last_update...
2025-07-03 13:19:22 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:22 - devo_backup - DEBUG - Executing query (attempt 1): from system.windows.eventlog select max(time) as last_update...
2025-07-03 13:19:22 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:22 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:23 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:23 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:23 - devo_backup - INFO - Executing Devo query: from user.activity.log select max(time) as last_update...
2025-07-03 13:19:23 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:23 - devo_backup - DEBUG - Executing query (attempt 1): from user.activity.log select max(time) as last_update...
2025-07-03 13:19:23 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:23 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:23 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:23 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:23 - devo_backup - INFO - Executing Devo query: from system.windows.eventlog select max(_time) as last_update...
2025-07-03 13:19:23 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:23 - devo_backup - DEBUG - Executing query (attempt 1): from system.windows.eventlog select max(_time) as last_update...
2025-07-03 13:19:23 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:23 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:23 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:23 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:23 - devo_backup - INFO - Executing Devo query: from system.linux.syslog select max(date) as last_update...
2025-07-03 13:19:23 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:23 - devo_backup - DEBUG - Executing query (attempt 1): from system.linux.syslog select max(date) as last_update...
2025-07-03 13:19:23 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:23 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:23 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:23 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:23 - devo_backup - INFO - Executing Devo query: from user.activity.log select max(_time) as last_update...
2025-07-03 13:19:23 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:23 - devo_backup - DEBUG - Executing query (attempt 1): from user.activity.log select max(_time) as last_update...
2025-07-03 13:19:23 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:23 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:23 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:23 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:23 - devo_backup - INFO - Executing Devo query: from system.windows.eventlog select max(created_at) as last_update...
2025-07-03 13:19:23 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:23 - devo_backup - DEBUG - Executing query (attempt 1): from system.windows.eventlog select max(created_at) as last_update...
2025-07-03 13:19:23 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:23 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:23 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:23 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:23 - devo_backup - INFO - Executing Devo query: from system.linux.syslog select max(time) as last_update...
2025-07-03 13:19:23 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:23 - devo_backup - DEBUG - Executing query (attempt 1): from system.linux.syslog select max(time) as last_update...
2025-07-03 13:19:23 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:23 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:23 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:23 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:23 - devo_backup - INFO - Executing Devo query: from user.activity.log select max(created_at) as last_update...
2025-07-03 13:19:23 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:23 - devo_backup - DEBUG - Executing query (attempt 1): from user.activity.log select max(created_at) as last_update...
2025-07-03 13:19:23 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:23 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:23 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:23 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:23 - devo_backup - INFO - Executing Devo query: from system.windows.eventlog select max(updated_at) as last_update...
2025-07-03 13:19:23 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:23 - devo_backup - DEBUG - Executing query (attempt 1): from system.windows.eventlog select max(updated_at) as last_update...
2025-07-03 13:19:23 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:23 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:23 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:23 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:23 - devo_backup - INFO - Executing Devo query: from system.linux.syslog select max(_time) as last_update...
2025-07-03 13:19:23 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:23 - devo_backup - DEBUG - Executing query (attempt 1): from system.linux.syslog select max(_time) as last_update...
2025-07-03 13:19:23 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:23 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:23 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:23 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:23 - devo_backup - INFO - Executing Devo query: from user.activity.log select max(updated_at) as last_update...
2025-07-03 13:19:23 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:23 - devo_backup - DEBUG - Executing query (attempt 1): from user.activity.log select max(updated_at) as last_update...
2025-07-03 13:19:23 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:23 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:23 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:23 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:23 - devo_backup - DEBUG - Successfully analyzed table: system.windows.eventlog
2025-07-03 13:19:23 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:23 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:23 - devo_backup - INFO - Executing Devo query: from system.linux.syslog select max(created_at) as last_update...
2025-07-03 13:19:23 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:23 - devo_backup - DEBUG - Executing query (attempt 1): from system.linux.syslog select max(created_at) as last_update...
2025-07-03 13:19:23 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:23 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:23 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:23 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:23 - devo_backup - DEBUG - Successfully analyzed table: user.activity.log
2025-07-03 13:19:23 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:23 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:23 - devo_backup - INFO - Executing Devo query: from system.linux.syslog select max(updated_at) as last_update...
2025-07-03 13:19:23 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:19:23 - devo_backup - DEBUG - Executing query (attempt 1): from system.linux.syslog select max(updated_at) as last_update...
2025-07-03 13:19:23 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:19:23 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:19:23 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:19:23 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:19:23 - devo_backup - DEBUG - Successfully analyzed table: system.linux.syslog
2025-07-03 13:19:23 - devo_backup - INFO - Progress: 28/28 tables analyzed
2025-07-03 13:19:23 - devo_backup - INFO - Discovery results saved to: results\table_discovery_results_20250703_131923.json
2025-07-03 13:19:23 - devo_backup - INFO - Table discovery completed successfully!
