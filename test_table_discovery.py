#!/usr/bin/env python3
"""
Test script for the Devo Table Discovery system.
This script tests the table discovery functionality and validates the results.
"""

import sys
import json
import os
from datetime import datetime

# Import the table discovery module
from table_discovery import TableDiscovery, TableDiscoveryError


def test_table_discovery():
    """Test the table discovery functionality."""
    print("Testing Devo Table Discovery System")
    print("=" * 50)
    
    try:
        # Initialize discovery system
        print("1. Initializing table discovery system...")
        discovery = TableDiscovery()
        print("✓ Table discovery system initialized successfully")
        
        # Test connection
        print("\n2. Testing connection to Devo API...")
        if discovery._test_connection():
            print("✓ Connection to Devo API successful")
        else:
            print("✗ Connection to Devo API failed")
            return False
        
        # Test table list discovery
        print("\n3. Testing table list discovery...")
        table_list = discovery._discover_table_list()
        print(f"✓ Discovered {len(table_list)} tables")
        
        if table_list:
            print("Sample discovered tables:")
            for i, table in enumerate(table_list[:5], 1):
                print(f"  {i}. {table}")
            if len(table_list) > 5:
                print(f"  ... and {len(table_list) - 5} more tables")
        
        # Test single table analysis
        if table_list:
            print(f"\n4. Testing single table analysis...")
            test_table = table_list[0]
            print(f"Analyzing table: {test_table}")
            
            table_info = discovery._analyze_single_table(test_table)
            print(f"✓ Table analysis completed")
            print(f"  - Success: {table_info['success']}")
            print(f"  - Estimated records: {table_info.get('estimated_records', 0)}")
            print(f"  - Schema fields: {len(table_info.get('schema', {}).get('fields', []))}")
        
        # Test results generation
        print("\n5. Testing results generation...")
        discovery.discovered_tables = {table_list[0]: discovery._analyze_single_table(table_list[0])} if table_list else {}
        discovery.stats['total_tables_found'] = len(table_list)
        discovery.stats['successful_discoveries'] = 1 if table_list else 0
        discovery.stats['end_time'] = datetime.now()
        
        results = discovery._generate_final_results()
        print("✓ Results generation successful")
        print(f"  - Total tables: {results['statistics']['total_tables_found']}")
        print(f"  - Successful discoveries: {results['statistics']['successful_discoveries']}")
        
        # Test file saving
        print("\n6. Testing results file saving...")
        output_file = discovery.save_results(results, "test_discovery_results.json")
        print(f"✓ Results saved to: {output_file}")
        
        # Verify file exists and is valid JSON
        if os.path.exists(output_file):
            with open(output_file, 'r') as f:
                saved_results = json.load(f)
            print("✓ Saved file is valid JSON")
            
            # Clean up test file
            os.remove(output_file)
            print("✓ Test file cleaned up")
        
        print("\n" + "=" * 50)
        print("✓ All tests passed successfully!")
        return True
        
    except Exception as e:
        print(f"\n✗ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_pattern_scanning():
    """Test the pattern scanning functionality."""
    print("\nTesting Pattern Scanning")
    print("-" * 30)
    
    try:
        discovery = TableDiscovery()
        
        # Test common patterns
        print("Testing common table patterns...")
        pattern_tables = discovery._scan_common_patterns()
        print(f"✓ Found {len(pattern_tables)} tables via pattern scanning")
        
        if pattern_tables:
            print("Sample pattern-discovered tables:")
            for i, table in enumerate(pattern_tables[:3], 1):
                print(f"  {i}. {table}")
        
        return True
        
    except Exception as e:
        print(f"✗ Pattern scanning test failed: {str(e)}")
        return False


def test_system_queries():
    """Test the system query functionality."""
    print("\nTesting System Queries")
    print("-" * 25)
    
    try:
        discovery = TableDiscovery()
        
        # Test system table queries
        print("Testing system table queries...")
        system_tables = discovery._query_system_tables()
        print(f"✓ System query completed, found {len(system_tables)} tables")
        
        if system_tables:
            print("Sample system-discovered tables:")
            for i, table in enumerate(system_tables[:3], 1):
                print(f"  {i}. {table}")
        
        return True
        
    except Exception as e:
        print(f"✗ System query test failed: {str(e)}")
        return False


def main():
    """Run all tests."""
    print("Devo Table Discovery - Test Suite")
    print("=" * 60)
    
    all_passed = True
    
    # Run main functionality test
    if not test_table_discovery():
        all_passed = False
    
    # Run pattern scanning test
    if not test_pattern_scanning():
        all_passed = False
    
    # Run system queries test
    if not test_system_queries():
        all_passed = False
    
    # Final result
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 ALL TESTS PASSED! Table discovery system is working correctly.")
        sys.exit(0)
    else:
        print("❌ SOME TESTS FAILED! Please check the errors above.")
        sys.exit(1)


if __name__ == "__main__":
    main()
