"""
Configuration module for Devo API backup system.
Handles environment variables and application settings.
"""

import os
from typing import Optional
from dotenv import load_dotenv


class Config:
    """Configuration class to manage environment variables and settings."""
    
    def __init__(self):
        """Initialize configuration by loading environment variables."""
        load_dotenv()
        self._validate_required_env_vars()
    
    @property
    def devo_api_key(self) -> str:
        """Get Devo API key from environment."""
        return os.getenv('DEVO_API_KEY', '')
    
    @property
    def devo_api_secret(self) -> str:
        """Get Devo API secret from environment."""
        return os.getenv('DEVO_API_SECRET', '')
    
    @property
    def devo_query_endpoint(self) -> str:
        """Get Devo query endpoint from environment."""
        return os.getenv('DEVO_QUERY_ENDPOINT', 'https://api-apac.devo.com/search/query')
    
    @property
    def oss_access_key_id(self) -> str:
        """Get OSS access key ID from environment."""
        return os.getenv('OSS_ACCESS_KEY_ID', '')
    
    @property
    def oss_access_key_secret(self) -> str:
        """Get OSS access key secret from environment."""
        return os.getenv('OSS_ACCESS_KEY_SECRET', '')
    
    @property
    def oss_endpoint(self) -> str:
        """Get OSS endpoint from environment."""
        return os.getenv('OSS_ENDPOINT', '')
    
    @property
    def oss_bucket_name(self) -> str:
        """Get OSS bucket name from environment."""
        return os.getenv('OSS_BUCKET_NAME', '')
    
    # API Configuration
    @property
    def api_timeout(self) -> int:
        """Get API timeout in seconds."""
        return int(os.getenv('API_TIMEOUT', '300'))  # 5 minutes default
    
    @property
    def max_retries(self) -> int:
        """Get maximum number of API retries."""
        return int(os.getenv('MAX_RETRIES', '3'))
    
    @property
    def retry_delay(self) -> float:
        """Get delay between retries in seconds."""
        return float(os.getenv('RETRY_DELAY', '5.0'))
    
    @property
    def rate_limit_delay(self) -> float:
        """Get rate limit delay in seconds."""
        return float(os.getenv('RATE_LIMIT_DELAY', '1.0'))
    
    def _validate_required_env_vars(self) -> None:
        """Validate that all required environment variables are set."""
        required_vars = [
            'DEVO_API_KEY',
            'DEVO_API_SECRET',
            'DEVO_QUERY_ENDPOINT'
        ]
        
        missing_vars = []
        for var in required_vars:
            if not os.getenv(var):
                missing_vars.append(var)
        
        if missing_vars:
            raise ValueError(
                f"Missing required environment variables: {', '.join(missing_vars)}. "
                "Please check your .env file."
            )
    
    def get_backup_path(self, date_str: str) -> str:
        """Generate backup path for a given date."""
        return f"Devo/backup/{date_str}"
    
    def __str__(self) -> str:
        """String representation of configuration (without sensitive data)."""
        return (
            f"Config("
            f"endpoint={self.devo_query_endpoint}, "
            f"timeout={self.api_timeout}, "
            f"max_retries={self.max_retries}, "
            f"rate_limit_delay={self.rate_limit_delay}"
            f")"
        )


# Global configuration instance
config = Config()
