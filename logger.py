"""
Logging configuration for the Devo API backup system.
Provides structured logging with file and console output.
"""

import logging
import os
from datetime import datetime
from typing import Optional


class BackupLogger:
    """Custom logger for the backup system."""
    
    def __init__(self, name: str = "devo_backup", log_level: str = "INFO"):
        """
        Initialize the logger.
        
        Args:
            name: Logger name
            log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        """
        self.logger = logging.getLogger(name)
        self.logger.setLevel(getattr(logging, log_level.upper()))
        
        # Prevent duplicate handlers
        if not self.logger.handlers:
            self._setup_handlers()
    
    def _setup_handlers(self):
        """Set up file and console handlers."""
        # Create logs directory if it doesn't exist
        os.makedirs("logs", exist_ok=True)
        
        # File handler with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file = f"logs/devo_backup_{timestamp}.log"
        
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # Formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
        
        self.logger.info(f"Logging initialized. Log file: {log_file}")
    
    def debug(self, message: str, **kwargs):
        """Log debug message."""
        self.logger.debug(message, **kwargs)
    
    def info(self, message: str, **kwargs):
        """Log info message."""
        self.logger.info(message, **kwargs)
    
    def warning(self, message: str, **kwargs):
        """Log warning message."""
        self.logger.warning(message, **kwargs)
    
    def error(self, message: str, **kwargs):
        """Log error message."""
        self.logger.error(message, **kwargs)
    
    def critical(self, message: str, **kwargs):
        """Log critical message."""
        self.logger.critical(message, **kwargs)
    
    def exception(self, message: str, **kwargs):
        """Log exception with traceback."""
        self.logger.exception(message, **kwargs)
    
    def log_api_request(self, method: str, url: str, status_code: Optional[int] = None):
        """Log API request details."""
        if status_code:
            self.info(f"API {method} {url} - Status: {status_code}")
        else:
            self.info(f"API {method} {url} - Request sent")
    
    def log_data_pull(self, date: str, record_count: int, success: bool = True):
        """Log data pull results."""
        status = "SUCCESS" if success else "FAILED"
        self.info(f"Data pull for {date}: {status} - Records: {record_count}")
    
    def log_backup_summary(self, total_dates: int, successful_dates: int, failed_dates: int):
        """Log backup operation summary."""
        self.info(f"Backup Summary - Total: {total_dates}, Success: {successful_dates}, Failed: {failed_dates}")
    
    def log_rate_limit(self, delay: float):
        """Log rate limiting."""
        self.warning(f"Rate limit encountered. Waiting {delay} seconds...")
    
    def log_retry(self, attempt: int, max_attempts: int, error: str):
        """Log retry attempt."""
        self.warning(f"Retry attempt {attempt}/{max_attempts} - Error: {error}")


# Global logger instance
logger = BackupLogger()
