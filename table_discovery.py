#!/usr/bin/env python3
"""
Devo Table Discovery Script

This script scans and discovers all tables that exist in the Devo environment.
It queries the system to enumerate all available tables/datasets and provides
comprehensive information about each table including schemas, record counts,
and metadata.

Usage:
    python table_discovery.py                    # Discover all tables
    python table_discovery.py --output custom.json  # Custom output file
    python table_discovery.py --verbose          # Verbose logging
"""

import sys
import json
import os
import argparse
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed
import time

from config import config
from logger import logger
from date_utils import DateUtils
from devo_client import DevoClient, DevoAPIError


class TableDiscoveryError(Exception):
    """Custom exception for table discovery errors."""
    pass


class TableDiscovery:
    """Comprehensive table discovery system for Devo environment."""

    def __init__(self):
        """Initialize the table discovery system."""
        self.devo_client = DevoClient()
        self.discovered_tables = {}
        self.stats = {
            'start_time': datetime.now(),
            'end_time': None,
            'total_tables_found': 0,
            'successful_discoveries': 0,
            'failed_discoveries': 0,
            'total_records_estimated': 0,
            'discovery_errors': []
        }
        
    def discover_all_tables(self) -> Dict[str, Any]:
        """
        Discover all tables in the Devo environment.
        
        Returns:
            Dictionary containing all discovered table information
        """
        logger.info("Starting comprehensive table discovery...")
        
        try:
            # Test connection first
            if not self._test_connection():
                raise TableDiscoveryError("Failed to connect to Devo API")
            
            # Step 1: Get list of all available tables
            logger.info("Step 1: Discovering available tables...")
            table_list = self._discover_table_list()
            
            if not table_list:
                logger.warning("No tables discovered. This might indicate an issue with table enumeration.")
                return self._generate_final_results()
            
            logger.info(f"Found {len(table_list)} tables to analyze")
            self.stats['total_tables_found'] = len(table_list)
            
            # Step 2: Analyze each table for detailed information
            logger.info("Step 2: Analyzing table details...")
            self._analyze_tables(table_list)
            
            # Step 3: Generate final results
            return self._generate_final_results()
            
        except Exception as e:
            logger.exception(f"Table discovery failed: {str(e)}")
            self.stats['discovery_errors'].append(str(e))
            return self._generate_final_results()
        finally:
            self.stats['end_time'] = datetime.now()
    
    def _test_connection(self) -> bool:
        """Test connection to Devo API."""
        logger.info("Testing connection to Devo API...")
        return self.devo_client.test_connection()
    
    def _discover_table_list(self) -> List[str]:
        """
        Discover all available tables in the Devo environment.
        
        Returns:
            List of table names
        """
        table_list = []
        
        try:
            # Method 1: Try to query system tables for table metadata
            logger.info("Attempting to discover tables via system metadata...")
            system_tables = self._query_system_tables()
            if system_tables:
                table_list.extend(system_tables)
                logger.info(f"Found {len(system_tables)} tables via system metadata")
            
            # Method 2: Try common table patterns and namespaces
            logger.info("Scanning common table patterns...")
            pattern_tables = self._scan_common_patterns()
            if pattern_tables:
                # Add new tables not already found
                new_tables = [t for t in pattern_tables if t not in table_list]
                table_list.extend(new_tables)
                logger.info(f"Found {len(new_tables)} additional tables via pattern scanning")
            
            # Method 3: Try to enumerate based on known prefixes
            logger.info("Scanning known prefixes...")
            prefix_tables = self._scan_known_prefixes()
            if prefix_tables:
                new_tables = [t for t in prefix_tables if t not in table_list]
                table_list.extend(new_tables)
                logger.info(f"Found {len(new_tables)} additional tables via prefix scanning")
            
        except Exception as e:
            logger.error(f"Error during table discovery: {str(e)}")
            self.stats['discovery_errors'].append(f"Table discovery error: {str(e)}")
        
        # Remove duplicates and sort
        table_list = sorted(list(set(table_list)))
        logger.info(f"Total unique tables discovered: {len(table_list)}")
        
        return table_list
    
    def _query_system_tables(self) -> List[str]:
        """
        Query Devo system tables to get list of available tables.
        
        Returns:
            List of table names from system metadata
        """
        tables = []
        
        # Common system queries to discover tables
        system_queries = [
            # Try to query table metadata if available
            "from siem.logtrust.table.info select table",
            "from system.table.metadata select table_name",
            "from devo.system.tables select name",
            # Alternative approaches
            "show tables",
            "describe tables"
        ]
        
        current_date = DateUtils.get_current_date()
        start_time, end_time = DateUtils.format_date_for_query(current_date)
        
        for query in system_queries:
            try:
                logger.debug(f"Trying system query: {query}")
                result = self.devo_client.query_data(query, start_time, end_time)
                
                if result and result.get('object'):
                    # Extract table names from result
                    for record in result['object']:
                        if isinstance(record, dict):
                            # Look for table name in various possible fields
                            for field in ['table', 'table_name', 'name', 'tablename']:
                                if field in record and record[field]:
                                    tables.append(str(record[field]))
                        elif isinstance(record, str):
                            tables.append(record)
                    
                    if tables:
                        logger.info(f"Successfully discovered tables using query: {query}")
                        break
                        
            except Exception as e:
                logger.debug(f"System query failed: {query} - {str(e)}")
                continue
        
        return list(set(tables))
    
    def _scan_common_patterns(self) -> List[str]:
        """
        Scan for tables using common Devo table patterns.

        Returns:
            List of table names found via pattern scanning
        """
        tables = []

        # Common Devo table patterns and namespaces
        common_patterns = [
            # SIEM and security tables
            "siem.logtrust.web.activity",
            "siem.logtrust.alert.info",
            "siem.logtrust.auth.info",
            "siem.logtrust.dns.info",
            "siem.logtrust.file.info",
            "siem.logtrust.network.info",
            "siem.logtrust.process.info",
            "siem.logtrust.registry.info",
            "siem.logtrust.system.info",

            # Firewall and network
            "firewall.all",
            "firewall.cisco.asa",
            "firewall.paloalto",
            "firewall.fortinet",
            "network.cisco.router",
            "network.switch.info",

            # Application logs
            "app.web.access",
            "app.web.error",
            "app.database.audit",
            "app.api.requests",

            # System logs
            "system.windows.eventlog",
            "system.linux.syslog",
            "system.auth.login",

            # Cloud services
            "cloud.aws.cloudtrail",
            "cloud.azure.activity",
            "cloud.gcp.audit",

            # Custom application tables (common patterns)
            "my.app.data",
            "custom.logs.info",
            "user.activity.log"
        ]

        # Add specific tables from user's environment
        user_specific_tables = [
            "my.app.tngd.actiontraillinux",
            "my.app.tngd.waf",
            "cloud.office365.management.exchange",
            "cloud.alibaba.log_service.events",
            "cef0.zscaler.nssweblog",
            "cloud.office365.management.airinvestigation",
            "edr.crowdstrike.falconstreaming.agents",
            "box.stat.unix.diskstat",
            "nac.aruba.clearpass.session",
            "my.app.tngd.ciscoswitch",
            "my.app.tngd.actiontrailwindows",
            "my.app.tngd.adminportal",
            "my.app.tngd.cfw",
            "my.app.tngd.cyberark",
            "my.app.tngd.ezeelogin",
            "my.app.tngd.h3ccoreswitch",
            "my.app.tngd.h3cswitch",
            "my.app.tngd.h3cwirelessctrl",
            "my.app.tngd.keeper",
            "my.app.tngd.polardb",
            "my.app.tngd.rds",
            "my.app.tngd.sas",
            "firewall.fortinet.event.connector",
            "firewall.fortinet.event.ha",
            "firewall.fortinet.event.sdwan",
            "firewall.fortinet.event.system",
            "firewall.fortinet.event.user",
            "firewall.fortinet.event.vpn",
            "firewall.fortinet.traffic.forward",
            "firewall.fortinet.utm.ssl",
            "firewall.fortinet.utm.webfilter",
            "cloud.office365.management.azureactivedirectory",
            "cloud.office365.management.complianceposturemanagement",
            "cloud.office365.management.copilot",
            "cloud.office365.management.endpoint",
            "cloud.office365.management.microsoftteams",
            "cloud.office365.management.microsofttodo",
            "cloud.office365.management.onedrive",
            "cloud.office365.management.planner",
            "cloud.office365.management.powerbi",
            "cloud.office365.management.powerplatform",
            "cloud.office365.management.publicendpoint",
            "cloud.office365.management.quarantine",
            "cloud.office365.management.securitycompliancecenter",
            "cloud.office365.management.sharepoint",
            "cloud.office365.management.threatintelligence",
            "cloud.office365.management.workplaceanalytics",
            "cloud.office365.management.yammer",
            "edr.crowdstrike.falconstreaming.alert",
            "edr.crowdstrike.falconstreaming.auth_activity",
            "edr.crowdstrike.falconstreaming.behaviors",
            "edr.crowdstrike.falconstreaming.detection_summary",
            "edr.crowdstrike.falconstreaming.epp_detection_summary",
            "edr.crowdstrike.falconstreaming.other",
            "edr.crowdstrike.falconstreaming.user_activity_all",
            "edr.crowdstrike.falconstreaming.user_activity_detections",
            "edr.crowdstrike.falconstreaming.user_activity_groups",
            "edr.crowdstrike.falconstreaming.user_activity_prevention_policy",
            "app.lark.audit.event",
            "firewall.fortinet.traffic.local",
            "netstat.zscaler.analyzer_zpa",
            "cloud.office365.management.microsoftflow",
            "cloud.office365.management.microsoftforms"
        ]

        # Combine common patterns with user-specific tables
        all_patterns = common_patterns + user_specific_tables

        current_date = DateUtils.get_current_date()
        start_time, end_time = DateUtils.format_date_for_query(current_date)

        logger.info(f"Testing {len(all_patterns)} table patterns ({len(common_patterns)} common + {len(user_specific_tables)} user-specific)...")

        for pattern in all_patterns:
            try:
                # Test if table exists by running a simple count query
                test_query = f"from {pattern} select count() as record_count"
                result = self.devo_client.query_data(test_query, start_time, end_time)
                
                if result and result.get('success'):
                    tables.append(pattern)
                    logger.debug(f"Found table: {pattern}")
                
                # Small delay to avoid overwhelming the API
                time.sleep(0.1)
                
            except Exception as e:
                logger.debug(f"Pattern {pattern} not found: {str(e)}")
                continue
        
        logger.info(f"Found {len(tables)} tables via pattern scanning")
        return tables
    
    def _scan_known_prefixes(self) -> List[str]:
        """
        Scan for tables using known prefixes and attempt enumeration.
        
        Returns:
            List of table names found via prefix scanning
        """
        tables = []
        
        # Known prefixes in Devo environments
        known_prefixes = [
            "siem.logtrust",
            "firewall",
            "network",
            "app",
            "system",
            "cloud",
            "security",
            "audit",
            "my",
            "custom",
            "user",
            "admin",
            "log",
            "event",
            "alert"
        ]
        
        # This is a simplified approach - in a real environment,
        # you might have access to table enumeration APIs
        logger.info(f"Scanning {len(known_prefixes)} known prefixes...")
        
        # For now, we'll use the pattern approach since direct enumeration
        # requires specific Devo API capabilities that may not be available
        
        return tables

    def _analyze_tables(self, table_list: List[str]) -> None:
        """
        Analyze each discovered table for detailed information.

        Args:
            table_list: List of table names to analyze
        """
        logger.info(f"Analyzing {len(table_list)} tables for detailed information...")

        # Use threading for parallel analysis to speed up the process
        max_workers = min(5, len(table_list))  # Limit concurrent requests

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit analysis tasks
            future_to_table = {
                executor.submit(self._analyze_single_table, table): table
                for table in table_list
            }

            # Process completed tasks
            for i, future in enumerate(as_completed(future_to_table), 1):
                table = future_to_table[future]

                try:
                    table_info = future.result()
                    self.discovered_tables[table] = table_info

                    if table_info['success']:
                        self.stats['successful_discoveries'] += 1
                        self.stats['total_records_estimated'] += table_info.get('estimated_records', 0)
                    else:
                        self.stats['failed_discoveries'] += 1

                    # Progress indicator
                    if i % 10 == 0 or i == len(table_list):
                        logger.info(f"Progress: {i}/{len(table_list)} tables analyzed")

                except Exception as e:
                    logger.error(f"Failed to analyze table {table}: {str(e)}")
                    self.discovered_tables[table] = {
                        'success': False,
                        'error': str(e),
                        'table_name': table
                    }
                    self.stats['failed_discoveries'] += 1

    def _analyze_single_table(self, table_name: str) -> Dict[str, Any]:
        """
        Analyze a single table for detailed information.

        Args:
            table_name: Name of the table to analyze

        Returns:
            Dictionary with table analysis results
        """
        logger.debug(f"Analyzing table: {table_name}")

        table_info = {
            'table_name': table_name,
            'success': False,
            'schema': {},
            'estimated_records': 0,
            'last_updated': None,
            'size_info': {},
            'sample_data': [],
            'error': None,
            'analysis_timestamp': datetime.now().isoformat()
        }

        try:
            current_date = DateUtils.get_current_date()
            start_time, end_time = DateUtils.format_date_for_query(current_date)

            # Step 1: Get record count estimate
            count_info = self._get_table_record_count(table_name, start_time, end_time)
            table_info.update(count_info)

            # Step 2: Get schema information
            schema_info = self._get_table_schema(table_name, start_time, end_time)
            table_info['schema'] = schema_info

            # Step 3: Get sample data
            sample_info = self._get_table_sample(table_name, start_time, end_time)
            table_info['sample_data'] = sample_info

            # Step 4: Get last updated information
            last_updated = self._get_table_last_updated(table_name, start_time, end_time)
            table_info['last_updated'] = last_updated

            table_info['success'] = True
            logger.debug(f"Successfully analyzed table: {table_name}")

        except Exception as e:
            error_msg = str(e)
            logger.debug(f"Failed to analyze table {table_name}: {error_msg}")
            table_info['error'] = error_msg
            table_info['success'] = False

        return table_info

    def _get_table_record_count(self, table_name: str, start_time: str, end_time: str) -> Dict[str, Any]:
        """Get estimated record count for a table."""
        try:
            # Try to get count for current day
            count_query = f"from {table_name} select count() as record_count"
            result = self.devo_client.query_data(count_query, start_time, end_time)

            if result and result.get('object'):
                records = result['object']
                if records and len(records) > 0:
                    count = records[0].get('record_count', 0) if isinstance(records[0], dict) else 0
                    return {
                        'estimated_records': int(count) if count else 0,
                        'count_period': 'current_day'
                    }

            return {'estimated_records': 0, 'count_period': 'current_day'}

        except Exception as e:
            logger.debug(f"Failed to get record count for {table_name}: {str(e)}")
            return {'estimated_records': 0, 'count_period': 'unknown', 'count_error': str(e)}

    def _get_table_schema(self, table_name: str, start_time: str, end_time: str) -> Dict[str, Any]:
        """Get schema information for a table."""
        try:
            # Try to get a few records to infer schema
            schema_query = f"from {table_name} select * limit 5"
            result = self.devo_client.query_data(schema_query, start_time, end_time)

            schema_info = {
                'fields': [],
                'field_types': {},
                'inferred': True
            }

            if result and result.get('object'):
                records = result['object']
                if records and len(records) > 0:
                    # Analyze first record to infer schema
                    first_record = records[0]
                    if isinstance(first_record, dict):
                        for field, value in first_record.items():
                            schema_info['fields'].append(field)
                            # Infer type from value
                            if value is None:
                                field_type = 'unknown'
                            elif isinstance(value, bool):
                                field_type = 'boolean'
                            elif isinstance(value, int):
                                field_type = 'integer'
                            elif isinstance(value, float):
                                field_type = 'float'
                            elif isinstance(value, str):
                                field_type = 'string'
                            else:
                                field_type = 'object'

                            schema_info['field_types'][field] = field_type

            return schema_info

        except Exception as e:
            logger.debug(f"Failed to get schema for {table_name}: {str(e)}")
            return {'fields': [], 'field_types': {}, 'inferred': False, 'error': str(e)}

    def _get_table_sample(self, table_name: str, start_time: str, end_time: str) -> List[Dict[str, Any]]:
        """Get sample data from a table."""
        try:
            sample_query = f"from {table_name} select * limit 3"
            result = self.devo_client.query_data(sample_query, start_time, end_time)

            if result and result.get('object'):
                records = result['object']
                # Return up to 3 sample records
                return records[:3] if records else []

            return []

        except Exception as e:
            logger.debug(f"Failed to get sample data for {table_name}: {str(e)}")
            return []

    def _get_table_last_updated(self, table_name: str, start_time: str, end_time: str) -> Optional[str]:
        """Get last updated timestamp for a table."""
        try:
            # Try to get the most recent record timestamp
            # This assumes there's a timestamp field - common field names
            timestamp_fields = ['timestamp', 'eventdate', 'date', 'time', '_time', 'created_at', 'updated_at']

            for field in timestamp_fields:
                try:
                    last_query = f"from {table_name} select max({field}) as last_update"
                    result = self.devo_client.query_data(last_query, start_time, end_time)

                    if result and result.get('object'):
                        records = result['object']
                        if records and len(records) > 0:
                            last_update = records[0].get('last_update')
                            if last_update:
                                return str(last_update)
                except:
                    continue

            return None

        except Exception as e:
            logger.debug(f"Failed to get last updated for {table_name}: {str(e)}")
            return None

    def _generate_final_results(self) -> Dict[str, Any]:
        """Generate final results dictionary."""
        duration = (self.stats['end_time'] - self.stats['start_time']).total_seconds() if self.stats['end_time'] else 0

        results = {
            'discovery_metadata': {
                'timestamp': datetime.now().isoformat(),
                'discovery_duration_seconds': duration,
                'devo_endpoint': config.devo_query_endpoint,
                'discovery_version': '1.0.0'
            },
            'statistics': {
                'total_tables_found': self.stats['total_tables_found'],
                'successful_discoveries': self.stats['successful_discoveries'],
                'failed_discoveries': self.stats['failed_discoveries'],
                'total_estimated_records': self.stats['total_records_estimated'],
                'discovery_errors': self.stats['discovery_errors']
            },
            'tables': self.discovered_tables
        }

        return results

    def save_results(self, results: Dict[str, Any], output_file: Optional[str] = None) -> str:
        """
        Save discovery results to JSON file.

        Args:
            results: Discovery results dictionary
            output_file: Optional custom output filename

        Returns:
            Path to saved file
        """
        if output_file is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"table_discovery_results_{timestamp}.json"

        # Ensure results directory exists
        results_dir = "results"
        if not os.path.exists(results_dir):
            os.makedirs(results_dir)

        output_path = os.path.join(results_dir, output_file)

        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False, default=str)

            logger.info(f"Discovery results saved to: {output_path}")
            return output_path

        except Exception as e:
            logger.error(f"Failed to save results to {output_path}: {str(e)}")
            raise TableDiscoveryError(f"Failed to save results: {str(e)}")

    def print_summary(self, results: Dict[str, Any]) -> None:
        """Print a summary of discovery results."""
        stats = results['statistics']

        print("\n" + "="*60)
        print("DEVO TABLE DISCOVERY SUMMARY")
        print("="*60)
        print(f"Discovery completed: {results['discovery_metadata']['timestamp']}")
        print(f"Duration: {results['discovery_metadata']['discovery_duration_seconds']:.2f} seconds")
        print(f"Devo endpoint: {results['discovery_metadata']['devo_endpoint']}")
        print()
        print(f"Total tables found: {stats['total_tables_found']}")
        print(f"Successful discoveries: {stats['successful_discoveries']}")
        print(f"Failed discoveries: {stats['failed_discoveries']}")
        print(f"Total estimated records: {stats['total_estimated_records']:,}")
        print()

        if stats['discovery_errors']:
            print("Discovery errors:")
            for error in stats['discovery_errors']:
                print(f"  - {error}")
            print()

        # Show top tables by record count
        successful_tables = {
            name: info for name, info in results['tables'].items()
            if info.get('success', False)
        }

        if successful_tables:
            print("Top tables by estimated record count:")
            sorted_tables = sorted(
                successful_tables.items(),
                key=lambda x: x[1].get('estimated_records', 0),
                reverse=True
            )

            for i, (table_name, table_info) in enumerate(sorted_tables[:10], 1):
                record_count = table_info.get('estimated_records', 0)
                field_count = len(table_info.get('schema', {}).get('fields', []))
                print(f"  {i:2d}. {table_name}")
                print(f"      Records: {record_count:,}, Fields: {field_count}")

        print("="*60)


def parse_arguments() -> argparse.Namespace:
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Discover all tables in Devo environment",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python table_discovery.py                    # Discover all tables
  python table_discovery.py --output custom.json  # Custom output file
  python table_discovery.py --verbose          # Verbose logging
        """
    )

    parser.add_argument(
        '--output', '-o',
        type=str,
        help='Output filename for results (default: auto-generated with timestamp)'
    )

    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Enable verbose logging'
    )

    return parser.parse_args()


def main():
    """Main entry point."""
    try:
        # Parse arguments
        args = parse_arguments()

        # Set logging level
        if args.verbose:
            logger.logger.setLevel(logging.DEBUG)
            # Also set console handler to DEBUG
            for handler in logger.logger.handlers:
                if isinstance(handler, logging.StreamHandler) and not isinstance(handler, logging.FileHandler):
                    handler.setLevel(logging.DEBUG)

        logger.info("Starting Devo Table Discovery...")
        logger.info(f"Devo endpoint: {config.devo_query_endpoint}")

        # Initialize discovery system
        discovery = TableDiscovery()

        # Run discovery
        results = discovery.discover_all_tables()

        # Save results
        output_file = discovery.save_results(results, args.output)

        # Print summary
        discovery.print_summary(results)

        # Final status
        stats = results['statistics']
        if stats['failed_discoveries'] == 0:
            logger.info("Table discovery completed successfully!")
            print(f"\nResults saved to: {output_file}")
            sys.exit(0)
        else:
            logger.warning(f"Table discovery completed with {stats['failed_discoveries']} failures")
            print(f"\nResults saved to: {output_file}")
            sys.exit(1)

    except KeyboardInterrupt:
        logger.warning("Table discovery interrupted by user")
        sys.exit(130)
    except Exception as e:
        logger.exception(f"Table discovery failed: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
