#!/usr/bin/env python3
"""
Utility script to update the backup system with discovered tables.

This script reads the table discovery results and updates the devo_client.py
file to use the discovered tables instead of hardcoded ones.

Usage:
    python update_backup_tables.py                                    # Use latest discovery results
    python update_backup_tables.py --input custom_discovery.json      # Use specific discovery file
    python update_backup_tables.py --min-records 100                  # Only include tables with min records
    python update_backup_tables.py --dry-run                          # Show what would be changed without making changes
"""

import sys
import json
import os
import argparse
from typing import List, Dict, Any
from datetime import datetime


def load_discovery_results(input_file: str = None) -> Dict[str, Any]:
    """
    Load table discovery results from JSON file.
    
    Args:
        input_file: Path to discovery results file (if None, uses latest)
        
    Returns:
        Discovery results dictionary
    """
    if input_file is None:
        # Find the latest discovery results file
        results_dir = "results"
        if not os.path.exists(results_dir):
            raise FileNotFoundError("Results directory not found. Run table discovery first.")
        
        discovery_files = [
            f for f in os.listdir(results_dir) 
            if f.startswith("table_discovery_results_") and f.endswith(".json")
        ]
        
        if not discovery_files:
            raise FileNotFoundError("No table discovery results found. Run table discovery first.")
        
        # Sort by filename (which includes timestamp) to get the latest
        discovery_files.sort(reverse=True)
        input_file = os.path.join(results_dir, discovery_files[0])
        print(f"Using latest discovery results: {input_file}")
    
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        raise Exception(f"Failed to load discovery results from {input_file}: {str(e)}")


def filter_tables(discovery_results: Dict[str, Any], min_records: int = 0, 
                 include_empty: bool = True) -> List[str]:
    """
    Filter discovered tables based on criteria.
    
    Args:
        discovery_results: Discovery results dictionary
        min_records: Minimum number of records required
        include_empty: Whether to include tables with 0 records
        
    Returns:
        List of filtered table names
    """
    filtered_tables = []
    
    for table_name, table_info in discovery_results.get('tables', {}).items():
        # Skip failed discoveries
        if not table_info.get('success', False):
            continue
        
        # Check record count
        record_count = table_info.get('estimated_records', 0)
        
        if record_count >= min_records:
            filtered_tables.append(table_name)
        elif include_empty and record_count == 0:
            # Include empty tables if requested (they might have data on other days)
            filtered_tables.append(table_name)
    
    return sorted(filtered_tables)


def generate_table_list_code(tables: List[str]) -> str:
    """
    Generate Python code for the table list.
    
    Args:
        tables: List of table names
        
    Returns:
        Python code string
    """
    if not tables:
        return '        return []  # No tables discovered'
    
    # Format tables with proper indentation
    table_lines = []
    for table in tables:
        table_lines.append(f'            "{table}",')
    
    # Remove trailing comma from last item
    if table_lines:
        table_lines[-1] = table_lines[-1].rstrip(',')
    
    code = f"""        # Auto-generated from table discovery on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        # Total tables: {len(tables)}
        return [
{chr(10).join(table_lines)}
        ]"""
    
    return code


def update_devo_client(tables: List[str], dry_run: bool = False) -> bool:
    """
    Update the devo_client.py file with discovered tables.
    
    Args:
        tables: List of table names to use
        dry_run: If True, show changes without applying them
        
    Returns:
        True if successful, False otherwise
    """
    devo_client_file = "devo_client.py"
    
    if not os.path.exists(devo_client_file):
        print(f"Error: {devo_client_file} not found")
        return False
    
    try:
        # Read the current file
        with open(devo_client_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find the get_available_tables method
        method_start = content.find("def get_available_tables(self)")
        if method_start == -1:
            print("Error: get_available_tables method not found in devo_client.py")
            return False
        
        # Find the start of the return statement
        return_start = content.find("return [", method_start)
        if return_start == -1:
            # Try alternative format
            return_start = content.find("return", method_start)
            if return_start == -1:
                print("Error: return statement not found in get_available_tables method")
                return False
        
        # Find the end of the method (next method or class end)
        method_end = content.find("\n    def ", method_start + 1)
        if method_end == -1:
            # Try to find class end or file end
            method_end = content.find("\n\nclass", method_start + 1)
            if method_end == -1:
                method_end = len(content)
        
        # Extract the method content
        method_content = content[method_start:method_end]
        
        # Find the actual end of the return statement
        return_end = method_content.find("]", method_content.find("return ["))
        if return_end != -1:
            return_end = method_start + return_end + 1
        else:
            # Handle single line return or other formats
            return_end = method_start + method_content.find("\n", method_content.find("return")) 
        
        # Generate new method content
        new_method_lines = [
            "    def get_available_tables(self) -> List[str]:",
            "        \"\"\"",
            "        Get list of available tables from Devo.",
            "        Auto-updated from table discovery results.",
            "        ",
            "        Returns:",
            "            List of table names",
            "        \"\"\""
        ]
        
        new_table_code = generate_table_list_code(tables)
        new_method_lines.append(new_table_code)
        
        new_method_content = "\n".join(new_method_lines)
        
        # Replace the method
        new_content = content[:method_start] + new_method_content + content[method_end:]
        
        if dry_run:
            print("\n" + "="*60)
            print("DRY RUN - Changes that would be made:")
            print("="*60)
            print(f"File: {devo_client_file}")
            print(f"Tables to be added: {len(tables)}")
            print("\nNew get_available_tables method:")
            print("-" * 40)
            print(new_method_content)
            print("-" * 40)
            return True
        else:
            # Create backup
            backup_file = f"{devo_client_file}.backup.{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            with open(backup_file, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"Created backup: {backup_file}")
            
            # Write updated content
            with open(devo_client_file, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            print(f"Updated {devo_client_file} with {len(tables)} discovered tables")
            return True
    
    except Exception as e:
        print(f"Error updating {devo_client_file}: {str(e)}")
        return False


def print_summary(discovery_results: Dict[str, Any], filtered_tables: List[str]):
    """Print a summary of the update operation."""
    stats = discovery_results.get('statistics', {})
    
    print("\n" + "="*60)
    print("TABLE UPDATE SUMMARY")
    print("="*60)
    print(f"Discovery timestamp: {discovery_results.get('discovery_metadata', {}).get('timestamp', 'Unknown')}")
    print(f"Total tables discovered: {stats.get('total_tables_found', 0)}")
    print(f"Successful discoveries: {stats.get('successful_discoveries', 0)}")
    print(f"Tables selected for backup: {len(filtered_tables)}")
    print()
    
    if filtered_tables:
        print("Selected tables:")
        for i, table in enumerate(filtered_tables, 1):
            table_info = discovery_results.get('tables', {}).get(table, {})
            record_count = table_info.get('estimated_records', 0)
            print(f"  {i:2d}. {table} ({record_count:,} records)")
    
    print("="*60)


def parse_arguments() -> argparse.Namespace:
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Update backup system with discovered tables",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python update_backup_tables.py                           # Use latest discovery results
  python update_backup_tables.py --input custom.json      # Use specific file
  python update_backup_tables.py --min-records 100        # Only tables with 100+ records
  python update_backup_tables.py --dry-run                # Preview changes
        """
    )
    
    parser.add_argument(
        '--input', '-i',
        type=str,
        help='Input discovery results file (default: latest in results/)'
    )
    
    parser.add_argument(
        '--min-records', '-m',
        type=int,
        default=0,
        help='Minimum number of records required (default: 0)'
    )
    
    parser.add_argument(
        '--exclude-empty',
        action='store_true',
        help='Exclude tables with 0 records'
    )
    
    parser.add_argument(
        '--dry-run', '-n',
        action='store_true',
        help='Show what would be changed without making changes'
    )
    
    return parser.parse_args()


def main():
    """Main entry point."""
    try:
        args = parse_arguments()
        
        print("Devo Backup System - Table Update Utility")
        print("=" * 50)
        
        # Load discovery results
        print("Loading table discovery results...")
        discovery_results = load_discovery_results(args.input)
        
        # Filter tables
        print("Filtering tables based on criteria...")
        filtered_tables = filter_tables(
            discovery_results, 
            min_records=args.min_records,
            include_empty=not args.exclude_empty
        )
        
        if not filtered_tables:
            print("No tables match the specified criteria.")
            sys.exit(1)
        
        # Print summary
        print_summary(discovery_results, filtered_tables)
        
        # Update devo_client.py
        if args.dry_run:
            print("\nRunning in dry-run mode...")
        else:
            print(f"\nUpdating devo_client.py...")
        
        success = update_devo_client(filtered_tables, dry_run=args.dry_run)
        
        if success:
            if args.dry_run:
                print("\n✓ Dry run completed successfully!")
                print("Run without --dry-run to apply changes.")
            else:
                print("\n✓ Backup system updated successfully!")
                print("You can now run the backup system with the discovered tables.")
            sys.exit(0)
        else:
            print("\n✗ Failed to update backup system.")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\nOperation cancelled by user.")
        sys.exit(130)
    except Exception as e:
        print(f"\nError: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
