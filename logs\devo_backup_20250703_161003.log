2025-07-03 16:10:03 - devo_backup - INFO - Logging initialized. Log file: logs/devo_backup_20250703_161003.log
2025-07-03 16:10:03 - devo_backup - INFO - Starting Devo Data Volume Analysis...
2025-07-03 16:10:03 - devo_backup - INFO - Target date: 2025-07-01
2025-07-03 16:10:03 - devo_backup - INFO - Devo endpoint: https://api-apac.devo.com/search/query
2025-07-03 16:10:03 - devo_backup - INFO - Devo SDK client initialized with endpoint: https://api-apac.devo.com/search/query
2025-07-03 16:10:03 - devo_backup - INFO - Loading discovered tables...
2025-07-03 16:10:03 - devo_backup - INFO - Using latest discovery results: results\table_discovery_results_20250703_160303.json
2025-07-03 16:10:03 - devo_backup - INFO - Loaded 91 tables from discovery results
2025-07-03 16:10:03 - devo_backup - INFO - Starting data volume analysis for 91 tables on 2025-07-01
2025-07-03 16:10:03 - devo_backup - INFO - Testing connection to Devo API...
2025-07-03 16:10:03 - devo_backup - INFO - Testing connection to Devo API...
2025-07-03 16:10:03 - devo_backup - INFO - Executing Devo query: from siem.logtrust.web.activity select count() as test_count...
2025-07-03 16:10:03 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 16:10:03 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.web.activity select count() as test_count...
2025-07-03 16:10:03 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:03 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:03 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:03 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:03 - devo_backup - INFO - Devo API connection test successful
2025-07-03 16:10:03 - devo_backup - INFO - Analyzing table data volumes...
2025-07-03 16:10:03 - devo_backup - INFO - Starting parallel analysis with 8 workers...
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: app.lark.audit.event
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table app.lark.audit.event: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: app.web.error
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: app.api.requests
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table app.web.error: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: app.web.access
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: app.database.audit
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table app.api.requests: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: box.stat.unix.diskstat
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: cef0.zscaler.nssweblog
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: cloud.alibaba.log_service.events
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table app.web.access: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: cloud.aws.cloudtrail
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table app.database.audit: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: cloud.azure.activity
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: cloud.gcp.audit
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table box.stat.unix.diskstat: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table cef0.zscaler.nssweblog: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table cloud.alibaba.log_service.events: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: cloud.office365.management.airinvestigation
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table cloud.aws.cloudtrail: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: cloud.office365.management.azureactivedirectory
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table cloud.azure.activity: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table cloud.gcp.audit: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: cloud.office365.management.complianceposturemanagement
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: cloud.office365.management.copilot
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: cloud.office365.management.endpoint
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table cloud.office365.management.airinvestigation: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: cloud.office365.management.microsoftflow
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table cloud.office365.management.azureactivedirectory: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: cloud.office365.management.exchange
2025-07-03 16:10:03 - devo_backup - INFO - Progress: 10/91 tables analyzed
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: cloud.office365.management.microsoftforms
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table cloud.office365.management.complianceposturemanagement: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table cloud.office365.management.copilot: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table cloud.office365.management.endpoint: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: cloud.office365.management.microsoftteams
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table cloud.office365.management.microsoftflow: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: cloud.office365.management.microsofttodo
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table cloud.office365.management.exchange: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table cloud.office365.management.microsoftforms: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: cloud.office365.management.onedrive
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: cloud.office365.management.powerbi
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: cloud.office365.management.planner
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table cloud.office365.management.microsoftteams: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: cloud.office365.management.powerplatform
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table cloud.office365.management.microsofttodo: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: cloud.office365.management.quarantine
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: cloud.office365.management.publicendpoint
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table cloud.office365.management.onedrive: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table cloud.office365.management.powerbi: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table cloud.office365.management.planner: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: cloud.office365.management.securitycompliancecenter
2025-07-03 16:10:03 - devo_backup - INFO - Progress: 20/91 tables analyzed
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table cloud.office365.management.powerplatform: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: cloud.office365.management.sharepoint
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table cloud.office365.management.quarantine: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table cloud.office365.management.publicendpoint: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: cloud.office365.management.threatintelligence
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: cloud.office365.management.workplaceanalytics
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: cloud.office365.management.yammer
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table cloud.office365.management.securitycompliancecenter: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: custom.logs.info
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table cloud.office365.management.sharepoint: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: edr.crowdstrike.falconstreaming.agents
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: edr.crowdstrike.falconstreaming.alert
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table cloud.office365.management.threatintelligence: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table cloud.office365.management.workplaceanalytics: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table cloud.office365.management.yammer: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: edr.crowdstrike.falconstreaming.auth_activity
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table custom.logs.info: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: edr.crowdstrike.falconstreaming.behaviors
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table edr.crowdstrike.falconstreaming.agents: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table edr.crowdstrike.falconstreaming.alert: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: edr.crowdstrike.falconstreaming.detection_summary
2025-07-03 16:10:03 - devo_backup - INFO - Progress: 30/91 tables analyzed
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: edr.crowdstrike.falconstreaming.epp_detection_summary
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: edr.crowdstrike.falconstreaming.other
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table edr.crowdstrike.falconstreaming.auth_activity: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: edr.crowdstrike.falconstreaming.user_activity_all
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table edr.crowdstrike.falconstreaming.behaviors: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: edr.crowdstrike.falconstreaming.user_activity_detections
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: edr.crowdstrike.falconstreaming.user_activity_groups
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table edr.crowdstrike.falconstreaming.detection_summary: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table edr.crowdstrike.falconstreaming.epp_detection_summary: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table edr.crowdstrike.falconstreaming.other: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: edr.crowdstrike.falconstreaming.user_activity_prevention_policy
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table edr.crowdstrike.falconstreaming.user_activity_all: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: firewall.all
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table edr.crowdstrike.falconstreaming.user_activity_detections: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: firewall.fortinet
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: firewall.fortinet.event.connector
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: firewall.cisco.asa
2025-07-03 16:10:03 - devo_backup - INFO - Progress: 40/91 tables analyzed
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: firewall.fortinet.event.ha
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table edr.crowdstrike.falconstreaming.user_activity_prevention_policy: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: firewall.fortinet.event.sdwan
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table firewall.all: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table edr.crowdstrike.falconstreaming.user_activity_groups: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table firewall.fortinet: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table firewall.fortinet.event.connector: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table firewall.cisco.asa: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table firewall.fortinet.event.ha: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: firewall.fortinet.event.system
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table firewall.fortinet.event.sdwan: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: firewall.fortinet.event.user
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: firewall.fortinet.event.vpn
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: firewall.fortinet.traffic.forward
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: firewall.fortinet.traffic.local
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: firewall.fortinet.utm.ssl
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: firewall.fortinet.utm.webfilter
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table firewall.fortinet.event.system: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: firewall.paloalto
2025-07-03 16:10:03 - devo_backup - INFO - Progress: 50/91 tables analyzed
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table firewall.fortinet.event.user: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table firewall.fortinet.event.vpn: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table firewall.fortinet.traffic.forward: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table firewall.fortinet.traffic.local: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table firewall.fortinet.utm.ssl: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table firewall.fortinet.utm.webfilter: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: my.app.data
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table firewall.paloalto: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: my.app.tngd.actiontraillinux
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: my.app.tngd.actiontrailwindows
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: my.app.tngd.adminportal
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: my.app.tngd.cfw
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: my.app.tngd.ciscoswitch
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: my.app.tngd.cyberark
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table my.app.data: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: my.app.tngd.ezeelogin
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table my.app.tngd.actiontraillinux: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table my.app.tngd.actiontrailwindows: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table my.app.tngd.adminportal: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table my.app.tngd.cfw: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table my.app.tngd.ciscoswitch: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table my.app.tngd.cyberark: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: my.app.tngd.h3ccoreswitch
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table my.app.tngd.ezeelogin: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: my.app.tngd.h3cswitch
2025-07-03 16:10:03 - devo_backup - INFO - Progress: 60/91 tables analyzed
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: my.app.tngd.h3cwirelessctrl
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: my.app.tngd.keeper
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: my.app.tngd.polardb
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: my.app.tngd.rds
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: my.app.tngd.sas
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table my.app.tngd.h3ccoreswitch: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: my.app.tngd.waf
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table my.app.tngd.h3cswitch: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table my.app.tngd.h3cwirelessctrl: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table my.app.tngd.keeper: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table my.app.tngd.polardb: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table my.app.tngd.rds: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table my.app.tngd.sas: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: netstat.zscaler.analyzer_zpa
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table my.app.tngd.waf: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: nac.aruba.clearpass.session
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: network.switch.info
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: network.cisco.router
2025-07-03 16:10:03 - devo_backup - INFO - Progress: 70/91 tables analyzed
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: siem.logtrust.alert.info
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: siem.logtrust.auth.info
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: siem.logtrust.dns.info
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table netstat.zscaler.analyzer_zpa: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: siem.logtrust.network.info
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table nac.aruba.clearpass.session: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table network.switch.info: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table network.cisco.router: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table siem.logtrust.alert.info: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table siem.logtrust.auth.info: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table siem.logtrust.dns.info: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: siem.logtrust.process.info
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table siem.logtrust.network.info: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: siem.logtrust.file.info
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: siem.logtrust.registry.info
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: siem.logtrust.system.info
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: siem.logtrust.web.activity
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: system.linux.syslog
2025-07-03 16:10:03 - devo_backup - INFO - Progress: 80/91 tables analyzed
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: system.auth.login
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table siem.logtrust.process.info: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: system.windows.eventlog
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table siem.logtrust.file.info: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table siem.logtrust.registry.info: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table siem.logtrust.system.info: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table siem.logtrust.web.activity: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table system.linux.syslog: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table system.auth.login: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Analyzing data volume for table: user.activity.log
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table system.windows.eventlog: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - DEBUG - Failed to analyze table user.activity.log: expected string or bytes-like object, got 'datetime.date'
2025-07-03 16:10:03 - devo_backup - INFO - Progress: 90/91 tables analyzed
2025-07-03 16:10:03 - devo_backup - INFO - Progress: 91/91 tables analyzed
2025-07-03 16:10:03 - devo_backup - INFO - Analysis results saved to: results\data_volume_analysis_20250701_20250703_161003.json
2025-07-03 16:10:03 - devo_backup - WARNING - Data volume analysis completed with 91 failures
