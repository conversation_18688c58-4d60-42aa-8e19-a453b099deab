{"discovery_metadata": {"timestamp": "2025-07-03T16:03:03.735641", "discovery_duration_seconds": 0, "devo_endpoint": "https://api-apac.devo.com/search/query", "discovery_version": "1.0.0"}, "statistics": {"total_tables_found": 91, "successful_discoveries": 91, "failed_discoveries": 0, "total_estimated_records": 0, "discovery_errors": []}, "tables": {"app.lark.audit.event": {"table_name": "app.lark.audit.event", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:01:51.142081", "count_period": "current_day"}, "app.web.error": {"table_name": "app.web.error", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:01:51.147358", "count_period": "current_day"}, "app.api.requests": {"table_name": "app.api.requests", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:01:51.129944", "count_period": "current_day"}, "app.web.access": {"table_name": "app.web.access", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:01:51.144190", "count_period": "current_day"}, "app.database.audit": {"table_name": "app.database.audit", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:01:51.132046", "count_period": "current_day"}, "box.stat.unix.diskstat": {"table_name": "box.stat.unix.diskstat", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:01:54.513351", "count_period": "current_day"}, "cef0.zscaler.nssweblog": {"table_name": "cef0.zscaler.nssweblog", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:01:54.573068", "count_period": "current_day"}, "cloud.alibaba.log_service.events": {"table_name": "cloud.alibaba.log_service.events", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:01:54.576170", "count_period": "current_day"}, "cloud.aws.cloudtrail": {"table_name": "cloud.aws.cloudtrail", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:01:55.128695", "count_period": "current_day"}, "cloud.azure.activity": {"table_name": "cloud.azure.activity", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:01:56.150407", "count_period": "current_day"}, "cloud.gcp.audit": {"table_name": "cloud.gcp.audit", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:01:57.827455", "count_period": "current_day"}, "cloud.office365.management.airinvestigation": {"table_name": "cloud.office365.management.airinvestigation", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:01:58.061706", "count_period": "current_day"}, "cloud.office365.management.azureactivedirectory": {"table_name": "cloud.office365.management.azureactivedirectory", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:01:58.089193", "count_period": "current_day"}, "cloud.office365.management.complianceposturemanagement": {"table_name": "cloud.office365.management.complianceposturemanagement", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:01:58.536649", "count_period": "current_day"}, "cloud.office365.management.copilot": {"table_name": "cloud.office365.management.copilot", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:01:59.526613", "count_period": "current_day"}, "cloud.office365.management.endpoint": {"table_name": "cloud.office365.management.endpoint", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:01.833464", "count_period": "current_day"}, "cloud.office365.management.microsoftflow": {"table_name": "cloud.office365.management.microsoftflow", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:02.128820", "count_period": "current_day"}, "cloud.office365.management.exchange": {"table_name": "cloud.office365.management.exchange", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:01.834150", "count_period": "current_day"}, "cloud.office365.management.microsoftforms": {"table_name": "cloud.office365.management.microsoftforms", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:02.661027", "count_period": "current_day"}, "cloud.office365.management.microsoftteams": {"table_name": "cloud.office365.management.microsoftteams", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:03.845257", "count_period": "current_day"}, "cloud.office365.management.microsofttodo": {"table_name": "cloud.office365.management.microsofttodo", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:05.352924", "count_period": "current_day"}, "cloud.office365.management.onedrive": {"table_name": "cloud.office365.management.onedrive", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:05.743733", "count_period": "current_day"}, "cloud.office365.management.powerbi": {"table_name": "cloud.office365.management.powerbi", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:06.393848", "count_period": "current_day"}, "cloud.office365.management.planner": {"table_name": "cloud.office365.management.planner", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:06.392602", "count_period": "current_day"}, "cloud.office365.management.powerplatform": {"table_name": "cloud.office365.management.powerplatform", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:07.871418", "count_period": "current_day"}, "cloud.office365.management.quarantine": {"table_name": "cloud.office365.management.quarantine", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:10.213814", "count_period": "current_day"}, "cloud.office365.management.publicendpoint": {"table_name": "cloud.office365.management.publicendpoint", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:09.631838", "count_period": "current_day"}, "cloud.office365.management.securitycompliancecenter": {"table_name": "cloud.office365.management.securitycompliancecenter", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:10.742116", "count_period": "current_day"}, "cloud.office365.management.sharepoint": {"table_name": "cloud.office365.management.sharepoint", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:11.497858", "count_period": "current_day"}, "cloud.office365.management.threatintelligence": {"table_name": "cloud.office365.management.threatintelligence", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:11.828864", "count_period": "current_day"}, "cloud.office365.management.workplaceanalytics": {"table_name": "cloud.office365.management.workplaceanalytics", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:14.745704", "count_period": "current_day"}, "cloud.office365.management.yammer": {"table_name": "cloud.office365.management.yammer", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:15.611677", "count_period": "current_day"}, "custom.logs.info": {"table_name": "custom.logs.info", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:15.644727", "count_period": "current_day"}, "edr.crowdstrike.falconstreaming.agents": {"table_name": "edr.crowdstrike.falconstreaming.agents", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:16.021916", "count_period": "current_day"}, "edr.crowdstrike.falconstreaming.alert": {"table_name": "edr.crowdstrike.falconstreaming.alert", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:16.953916", "count_period": "current_day"}, "edr.crowdstrike.falconstreaming.auth_activity": {"table_name": "edr.crowdstrike.falconstreaming.auth_activity", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:19.450664", "count_period": "current_day"}, "edr.crowdstrike.falconstreaming.behaviors": {"table_name": "edr.crowdstrike.falconstreaming.behaviors", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:20.343583", "count_period": "current_day"}, "edr.crowdstrike.falconstreaming.detection_summary": {"table_name": "edr.crowdstrike.falconstreaming.detection_summary", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:20.603000", "count_period": "current_day"}, "edr.crowdstrike.falconstreaming.epp_detection_summary": {"table_name": "edr.crowdstrike.falconstreaming.epp_detection_summary", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:20.711085", "count_period": "current_day"}, "edr.crowdstrike.falconstreaming.other": {"table_name": "edr.crowdstrike.falconstreaming.other", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:21.803661", "count_period": "current_day"}, "edr.crowdstrike.falconstreaming.user_activity_all": {"table_name": "edr.crowdstrike.falconstreaming.user_activity_all", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:24.657123", "count_period": "current_day"}, "edr.crowdstrike.falconstreaming.user_activity_detections": {"table_name": "edr.crowdstrike.falconstreaming.user_activity_detections", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:24.753767", "count_period": "current_day"}, "edr.crowdstrike.falconstreaming.user_activity_groups": {"table_name": "edr.crowdstrike.falconstreaming.user_activity_groups", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:25.194304", "count_period": "current_day"}, "edr.crowdstrike.falconstreaming.user_activity_prevention_policy": {"table_name": "edr.crowdstrike.falconstreaming.user_activity_prevention_policy", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:25.195351", "count_period": "current_day"}, "firewall.all": {"table_name": "firewall.all", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:26.430402", "count_period": "current_day"}, "firewall.fortinet": {"table_name": "firewall.fortinet", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:29.436477", "count_period": "current_day"}, "firewall.fortinet.event.connector": {"table_name": "firewall.fortinet.event.connector", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:30.094555", "count_period": "current_day"}, "firewall.cisco.asa": {"table_name": "firewall.cisco.asa", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:29.329730", "count_period": "current_day"}, "firewall.fortinet.event.ha": {"table_name": "firewall.fortinet.event.ha", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:30.224917", "count_period": "current_day"}, "firewall.fortinet.event.sdwan": {"table_name": "firewall.fortinet.event.sdwan", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:30.605100", "count_period": "current_day"}, "firewall.fortinet.event.system": {"table_name": "firewall.fortinet.event.system", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:32.149205", "count_period": "current_day"}, "firewall.fortinet.event.user": {"table_name": "firewall.fortinet.event.user", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:32.780391", "count_period": "current_day"}, "firewall.fortinet.event.vpn": {"table_name": "firewall.fortinet.event.vpn", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:33.143820", "count_period": "current_day"}, "firewall.fortinet.traffic.forward": {"table_name": "firewall.fortinet.traffic.forward", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:33.519019", "count_period": "current_day"}, "firewall.fortinet.traffic.local": {"table_name": "firewall.fortinet.traffic.local", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:33.726026", "count_period": "current_day"}, "firewall.fortinet.utm.ssl": {"table_name": "firewall.fortinet.utm.ssl", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:34.910886", "count_period": "current_day"}, "firewall.fortinet.utm.webfilter": {"table_name": "firewall.fortinet.utm.webfilter", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:35.504299", "count_period": "current_day"}, "firewall.paloalto": {"table_name": "firewall.paloalto", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:35.796239", "count_period": "current_day"}, "my.app.data": {"table_name": "my.app.data", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:36.256446", "count_period": "current_day"}, "my.app.tngd.actiontraillinux": {"table_name": "my.app.tngd.actiontraillinux", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:37.361859", "count_period": "current_day"}, "my.app.tngd.actiontrailwindows": {"table_name": "my.app.tngd.actiontrailwindows", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:38.721265", "count_period": "current_day"}, "my.app.tngd.adminportal": {"table_name": "my.app.tngd.adminportal", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:39.503701", "count_period": "current_day"}, "my.app.tngd.cfw": {"table_name": "my.app.tngd.cfw", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:39.753299", "count_period": "current_day"}, "my.app.tngd.ciscoswitch": {"table_name": "my.app.tngd.ciscoswitch", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:40.357699", "count_period": "current_day"}, "my.app.tngd.cyberark": {"table_name": "my.app.tngd.cyberark", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:41.441425", "count_period": "current_day"}, "my.app.tngd.ezeelogin": {"table_name": "my.app.tngd.ezeelogin", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:42.309247", "count_period": "current_day"}, "my.app.tngd.h3ccoreswitch": {"table_name": "my.app.tngd.h3ccoreswitch", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:42.572303", "count_period": "current_day"}, "my.app.tngd.h3cswitch": {"table_name": "my.app.tngd.h3cswitch", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:42.940411", "count_period": "current_day"}, "my.app.tngd.h3cwirelessctrl": {"table_name": "my.app.tngd.h3cwirelessctrl", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:44.121890", "count_period": "current_day"}, "my.app.tngd.keeper": {"table_name": "my.app.tngd.keeper", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:45.041309", "count_period": "current_day"}, "my.app.tngd.polardb": {"table_name": "my.app.tngd.polardb", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:45.869405", "count_period": "current_day"}, "my.app.tngd.rds": {"table_name": "my.app.tngd.rds", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:46.130668", "count_period": "current_day"}, "my.app.tngd.sas": {"table_name": "my.app.tngd.sas", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:46.403537", "count_period": "current_day"}, "my.app.tngd.waf": {"table_name": "my.app.tngd.waf", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:47.806411", "count_period": "current_day"}, "netstat.zscaler.analyzer_zpa": {"table_name": "netstat.zscaler.analyzer_zpa", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:49.701581", "count_period": "current_day"}, "nac.aruba.clearpass.session": {"table_name": "nac.aruba.clearpass.session", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:48.425274", "count_period": "current_day"}, "network.switch.info": {"table_name": "network.switch.info", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:50.240247", "count_period": "current_day"}, "network.cisco.router": {"table_name": "network.cisco.router", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:50.225856", "count_period": "current_day"}, "siem.logtrust.alert.info": {"table_name": "siem.logtrust.alert.info", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:51.366981", "count_period": "current_day"}, "siem.logtrust.auth.info": {"table_name": "siem.logtrust.auth.info", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:52.784111", "count_period": "current_day"}, "siem.logtrust.dns.info": {"table_name": "siem.logtrust.dns.info", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:52.928745", "count_period": "current_day"}, "siem.logtrust.network.info": {"table_name": "siem.logtrust.network.info", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:54.549552", "count_period": "current_day"}, "siem.logtrust.process.info": {"table_name": "siem.logtrust.process.info", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:54.825827", "count_period": "current_day"}, "siem.logtrust.file.info": {"table_name": "siem.logtrust.file.info", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:54.084572", "count_period": "current_day"}, "siem.logtrust.registry.info": {"table_name": "siem.logtrust.registry.info", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:56.970147", "count_period": "current_day"}, "siem.logtrust.system.info": {"table_name": "siem.logtrust.system.info", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:57.058289", "count_period": "current_day"}, "siem.logtrust.web.activity": {"table_name": "siem.logtrust.web.activity", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:58.499267", "count_period": "current_day"}, "system.linux.syslog": {"table_name": "system.linux.syslog", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:59.101427", "count_period": "current_day"}, "system.auth.login": {"table_name": "system.auth.login", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:02:59.012054", "count_period": "current_day"}, "system.windows.eventlog": {"table_name": "system.windows.eventlog", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:03:00.052308", "count_period": "current_day"}, "user.activity.log": {"table_name": "user.activity.log", "success": true, "schema": {"fields": [], "field_types": {}, "inferred": true}, "estimated_records": 0, "last_updated": null, "size_info": {}, "sample_data": [], "error": null, "analysis_timestamp": "2025-07-03T16:03:00.297685", "count_period": "current_day"}}}