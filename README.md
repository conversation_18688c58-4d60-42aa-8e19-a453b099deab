# Devo API Data Backup System

A robust data backup system that pulls data from Devo API with comprehensive error handling, rate limiting, and multiple execution modes.

## Features

- **Three Execution Modes**:
  - Default mode: Pull data for current day
  - Specific date mode: Pull data for a specific date
  - Date range mode: Pull data for a date range
- **Robust Error Handling**: Automatic retries, rate limiting, and connection recovery
- **Comprehensive Logging**: Detailed logs with file and console output
- **Data Integrity**: Ensures complete data retrieval with validation
- **Configurable**: Environment-based configuration for flexibility

## Installation

1. **Clone or download the project files**

2. **Install Python dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure environment variables**:
   - Ensure your `.env` file contains the required Devo API credentials
   - The system will validate required variables on startup

## Configuration

The system uses environment variables from the `.env` file:

### Required Variables
```env
DEVO_API_KEY=your_api_key_here
DEVO_API_SECRET=your_api_secret_here
DEVO_QUERY_ENDPOINT=https://api-apac.devo.com/search/query
```

### Optional Variables
```env
# API Configuration
API_TIMEOUT=300          # Request timeout in seconds (default: 300)
MAX_RETRIES=3           # Maximum retry attempts (default: 3)
RETRY_DELAY=5.0         # Delay between retries in seconds (default: 5.0)
RATE_LIMIT_DELAY=1.0    # Delay between requests in seconds (default: 1.0)
```

## Usage

### Default Mode (Current Day)
```bash
python main.py
```
Pulls all data for the current day.

### Specific Date Mode
```bash
python main.py 2024-01-15
```
Pulls all data for January 15, 2024.

### Date Range Mode
```bash
python main.py 2024-01-15 2024-01-20
```
Pulls all data from January 15 to January 20, 2024 (inclusive).

## File Structure

```
Tngd_backup/
├── main.py              # Main script and orchestrator
├── config.py            # Configuration management
├── devo_client.py       # Devo API client with error handling
├── date_utils.py        # Date parsing and validation utilities
├── arg_parser.py        # Command-line argument parser
├── logger.py            # Logging configuration
├── requirements.txt     # Python dependencies
├── README.md           # This file
├── .env                # Environment variables (already exists)
├── logs/               # Log files (created automatically)
└── results/            # Backup results (created automatically)
```

## Output

### Logs
- Console output with real-time progress
- Detailed log files in `logs/` directory with timestamps
- Separate log levels for debugging and monitoring

### Results
- JSON files in `results/` directory with complete backup results
- Includes metadata, statistics, and detailed results for each date/table
- Timestamped filenames for easy tracking

### Example Log Output
```
2024-01-15 10:30:00 - devo_backup - INFO - Default mode: Using current date 2024-01-15
2024-01-15 10:30:01 - devo_backup - INFO - Devo client initialized with endpoint: https://api-apac.devo.com/search/query
2024-01-15 10:30:02 - devo_backup - INFO - Testing connection to Devo API...
2024-01-15 10:30:03 - devo_backup - INFO - Devo API connection test successful
2024-01-15 10:30:04 - devo_backup - INFO - Starting backup for 1 date(s)
2024-01-15 10:30:05 - devo_backup - INFO - Processing date 1/1: 2024-01-15
```

## Error Handling

The system includes comprehensive error handling:

- **Connection Issues**: Automatic retries with exponential backoff
- **Rate Limiting**: Respects API rate limits with appropriate delays
- **Invalid Dates**: Validates date formats and ranges
- **API Errors**: Handles HTTP errors and malformed responses
- **Partial Failures**: Continues processing even if some tables fail

## Customization

### Adding Custom Tables
Edit the `get_available_tables()` method in `devo_client.py` to specify your Devo tables:

```python
def get_available_tables(self) -> List[str]:
    return [
        "your.custom.table1",
        "your.custom.table2",
        "firewall.all",
        # Add your tables here
    ]
```

### Modifying Queries
Customize the query logic in the `pull_data_for_date()` method in `devo_client.py`.

## Troubleshooting

### Common Issues

1. **Authentication Errors**:
   - Verify API key and secret in `.env` file
   - Check if credentials have expired
   - Ensure endpoint URL is correct

2. **Connection Timeouts**:
   - Increase `API_TIMEOUT` in `.env`
   - Check network connectivity
   - Verify Devo API endpoint accessibility

3. **Rate Limiting**:
   - Increase `RATE_LIMIT_DELAY` in `.env`
   - The system automatically handles rate limits

4. **Date Validation Errors**:
   - Ensure dates are in YYYY-MM-DD format
   - Check that dates are not in the future
   - Verify start date is before end date in range mode

### Getting Help
- Check log files in `logs/` directory for detailed error information
- Review the results JSON files for specific table failures
- Enable debug logging by modifying the log level in `logger.py`

## Development

### Running Tests
```bash
# Install test dependencies
pip install pytest pytest-cov requests-mock

# Run tests (when test files are created)
pytest tests/
```

### Code Structure
- Modular design with separate concerns
- Comprehensive error handling and logging
- Type hints for better code maintainability
- Configuration-driven approach for flexibility
