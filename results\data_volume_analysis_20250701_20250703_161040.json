{"analysis_metadata": {"timestamp": "2025-07-03T16:10:40.220454", "analysis_duration_seconds": 0, "target_date": "2025-07-01", "devo_endpoint": "https://api-apac.devo.com/search/query", "analysis_version": "1.0.0"}, "statistics": {"total_tables_analyzed": 91, "successful_queries": 91, "failed_queries": 0, "tables_with_data": 0, "tables_without_data": 91, "total_records_found": 0, "analysis_errors": []}, "table_volumes": {"app.web.error": {"table_name": "app.web.error", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.195, "error": null, "analysis_timestamp": "2025-07-03T16:10:38.120988"}, "app.api.requests": {"table_name": "app.api.requests", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.221, "error": null, "analysis_timestamp": "2025-07-03T16:10:38.122050"}, "app.lark.audit.event": {"table_name": "app.lark.audit.event", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.225, "error": null, "analysis_timestamp": "2025-07-03T16:10:38.119371"}, "app.web.access": {"table_name": "app.web.access", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.243, "error": null, "analysis_timestamp": "2025-07-03T16:10:38.125386"}, "cloud.alibaba.log_service.events": {"table_name": "cloud.alibaba.log_service.events", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.226, "error": null, "analysis_timestamp": "2025-07-03T16:10:38.144706"}, "cef0.zscaler.nssweblog": {"table_name": "cef0.zscaler.nssweblog", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.237, "error": null, "analysis_timestamp": "2025-07-03T16:10:38.139215"}, "box.stat.unix.diskstat": {"table_name": "box.stat.unix.diskstat", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.284, "error": null, "analysis_timestamp": "2025-07-03T16:10:38.135825"}, "app.database.audit": {"table_name": "app.database.audit", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.293, "error": null, "analysis_timestamp": "2025-07-03T16:10:38.130455"}, "cloud.aws.cloudtrail": {"table_name": "cloud.aws.cloudtrail", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.113, "error": null, "analysis_timestamp": "2025-07-03T16:10:38.316948"}, "cloud.azure.activity": {"table_name": "cloud.azure.activity", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.131, "error": null, "analysis_timestamp": "2025-07-03T16:10:38.347344"}, "cloud.gcp.audit": {"table_name": "cloud.gcp.audit", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.134, "error": null, "analysis_timestamp": "2025-07-03T16:10:38.347344"}, "cloud.office365.management.airinvestigation": {"table_name": "cloud.office365.management.airinvestigation", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.138, "error": null, "analysis_timestamp": "2025-07-03T16:10:38.371169"}, "cloud.office365.management.azureactivedirectory": {"table_name": "cloud.office365.management.azureactivedirectory", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.174, "error": null, "analysis_timestamp": "2025-07-03T16:10:38.373626"}, "cloud.office365.management.complianceposturemanagement": {"table_name": "cloud.office365.management.complianceposturemanagement", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.185, "error": null, "analysis_timestamp": "2025-07-03T16:10:38.380887"}, "cloud.office365.management.microsoftflow": {"table_name": "cloud.office365.management.microsoftflow", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.162, "error": null, "analysis_timestamp": "2025-07-03T16:10:38.436095"}, "cloud.office365.management.endpoint": {"table_name": "cloud.office365.management.endpoint", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.171, "error": null, "analysis_timestamp": "2025-07-03T16:10:38.432883"}, "cloud.office365.management.copilot": {"table_name": "cloud.office365.management.copilot", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.193, "error": null, "analysis_timestamp": "2025-07-03T16:10:38.431838"}, "cloud.office365.management.exchange": {"table_name": "cloud.office365.management.exchange", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.163, "error": null, "analysis_timestamp": "2025-07-03T16:10:38.481034"}, "cloud.office365.management.microsoftforms": {"table_name": "cloud.office365.management.microsoftforms", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.161, "error": null, "analysis_timestamp": "2025-07-03T16:10:38.484929"}, "cloud.office365.management.microsoftteams": {"table_name": "cloud.office365.management.microsoftteams", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.178, "error": null, "analysis_timestamp": "2025-07-03T16:10:38.510422"}, "cloud.office365.management.onedrive": {"table_name": "cloud.office365.management.onedrive", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.134, "error": null, "analysis_timestamp": "2025-07-03T16:10:38.565947"}, "cloud.office365.management.microsofttodo": {"table_name": "cloud.office365.management.microsofttodo", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.172, "error": null, "analysis_timestamp": "2025-07-03T16:10:38.549444"}, "cloud.office365.management.planner": {"table_name": "cloud.office365.management.planner", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.148, "error": null, "analysis_timestamp": "2025-07-03T16:10:38.608016"}, "cloud.office365.management.powerplatform": {"table_name": "cloud.office365.management.powerplatform", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.13, "error": null, "analysis_timestamp": "2025-07-03T16:10:38.626254"}, "cloud.office365.management.powerbi": {"table_name": "cloud.office365.management.powerbi", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.171, "error": null, "analysis_timestamp": "2025-07-03T16:10:38.601991"}, "cloud.office365.management.publicendpoint": {"table_name": "cloud.office365.management.publicendpoint", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.134, "error": null, "analysis_timestamp": "2025-07-03T16:10:38.648305"}, "cloud.office365.management.sharepoint": {"table_name": "cloud.office365.management.sharepoint", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.115, "error": null, "analysis_timestamp": "2025-07-03T16:10:38.701211"}, "cloud.office365.management.securitycompliancecenter": {"table_name": "cloud.office365.management.securitycompliancecenter", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.131, "error": null, "analysis_timestamp": "2025-07-03T16:10:38.689346"}, "cloud.office365.management.quarantine": {"table_name": "cloud.office365.management.quarantine", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.192, "error": null, "analysis_timestamp": "2025-07-03T16:10:38.645669"}, "cloud.office365.management.threatintelligence": {"table_name": "cloud.office365.management.threatintelligence", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.138, "error": null, "analysis_timestamp": "2025-07-03T16:10:38.722525"}, "cloud.office365.management.yammer": {"table_name": "cloud.office365.management.yammer", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.158, "error": null, "analysis_timestamp": "2025-07-03T16:10:38.761057"}, "edr.crowdstrike.falconstreaming.agents": {"table_name": "edr.crowdstrike.falconstreaming.agents", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.152, "error": null, "analysis_timestamp": "2025-07-03T16:10:38.789274"}, "custom.logs.info": {"table_name": "custom.logs.info", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.156, "error": null, "analysis_timestamp": "2025-07-03T16:10:38.785577"}, "cloud.office365.management.workplaceanalytics": {"table_name": "cloud.office365.management.workplaceanalytics", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.189, "error": null, "analysis_timestamp": "2025-07-03T16:10:38.760010"}, "edr.crowdstrike.falconstreaming.behaviors": {"table_name": "edr.crowdstrike.falconstreaming.behaviors", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.16, "error": null, "analysis_timestamp": "2025-07-03T16:10:38.839866"}, "edr.crowdstrike.falconstreaming.auth_activity": {"table_name": "edr.crowdstrike.falconstreaming.auth_activity", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.18, "error": null, "analysis_timestamp": "2025-07-03T16:10:38.823331"}, "edr.crowdstrike.falconstreaming.detection_summary": {"table_name": "edr.crowdstrike.falconstreaming.detection_summary", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.161, "error": null, "analysis_timestamp": "2025-07-03T16:10:38.862723"}, "edr.crowdstrike.falconstreaming.alert": {"table_name": "edr.crowdstrike.falconstreaming.alert", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.207, "error": null, "analysis_timestamp": "2025-07-03T16:10:38.817569"}, "edr.crowdstrike.falconstreaming.epp_detection_summary": {"table_name": "edr.crowdstrike.falconstreaming.epp_detection_summary", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.137, "error": null, "analysis_timestamp": "2025-07-03T16:10:38.922085"}, "edr.crowdstrike.falconstreaming.user_activity_all": {"table_name": "edr.crowdstrike.falconstreaming.user_activity_all", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.177, "error": null, "analysis_timestamp": "2025-07-03T16:10:38.948241"}, "edr.crowdstrike.falconstreaming.other": {"table_name": "edr.crowdstrike.falconstreaming.other", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.19, "error": null, "analysis_timestamp": "2025-07-03T16:10:38.947723"}, "edr.crowdstrike.falconstreaming.user_activity_detections": {"table_name": "edr.crowdstrike.falconstreaming.user_activity_detections", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.184, "error": null, "analysis_timestamp": "2025-07-03T16:10:38.951839"}, "edr.crowdstrike.falconstreaming.user_activity_groups": {"table_name": "edr.crowdstrike.falconstreaming.user_activity_groups", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.151, "error": null, "analysis_timestamp": "2025-07-03T16:10:39.002712"}, "edr.crowdstrike.falconstreaming.user_activity_prevention_policy": {"table_name": "edr.crowdstrike.falconstreaming.user_activity_prevention_policy", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.194, "error": null, "analysis_timestamp": "2025-07-03T16:10:39.004958"}, "firewall.all": {"table_name": "firewall.all", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.181, "error": null, "analysis_timestamp": "2025-07-03T16:10:39.028364"}, "firewall.fortinet": {"table_name": "firewall.fortinet", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.181, "error": null, "analysis_timestamp": "2025-07-03T16:10:39.029437"}, "firewall.fortinet.event.connector": {"table_name": "firewall.fortinet.event.connector", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.169, "error": null, "analysis_timestamp": "2025-07-03T16:10:39.062707"}, "firewall.fortinet.event.sdwan": {"table_name": "firewall.fortinet.event.sdwan", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.127, "error": null, "analysis_timestamp": "2025-07-03T16:10:39.141426"}, "firewall.fortinet.event.system": {"table_name": "firewall.fortinet.event.system", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.131, "error": null, "analysis_timestamp": "2025-07-03T16:10:39.155186"}, "firewall.cisco.asa": {"table_name": "firewall.cisco.asa", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.17, "error": null, "analysis_timestamp": "2025-07-03T16:10:39.127563"}, "firewall.fortinet.event.ha": {"table_name": "firewall.fortinet.event.ha", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.161, "error": null, "analysis_timestamp": "2025-07-03T16:10:39.140220"}, "firewall.fortinet.event.user": {"table_name": "firewall.fortinet.event.user", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.128, "error": null, "analysis_timestamp": "2025-07-03T16:10:39.200434"}, "firewall.fortinet.traffic.forward": {"table_name": "firewall.fortinet.traffic.forward", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.14, "error": null, "analysis_timestamp": "2025-07-03T16:10:39.213338"}, "firewall.fortinet.event.vpn": {"table_name": "firewall.fortinet.event.vpn", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.156, "error": null, "analysis_timestamp": "2025-07-03T16:10:39.212065"}, "firewall.fortinet.traffic.local": {"table_name": "firewall.fortinet.traffic.local", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.17, "error": null, "analysis_timestamp": "2025-07-03T16:10:39.233264"}, "firewall.fortinet.utm.webfilter": {"table_name": "firewall.fortinet.utm.webfilter", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.161, "error": null, "analysis_timestamp": "2025-07-03T16:10:39.287628"}, "firewall.fortinet.utm.ssl": {"table_name": "firewall.fortinet.utm.ssl", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.199, "error": null, "analysis_timestamp": "2025-07-03T16:10:39.269963"}, "my.app.tngd.actiontraillinux": {"table_name": "my.app.tngd.actiontraillinux", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.168, "error": null, "analysis_timestamp": "2025-07-03T16:10:39.331216"}, "my.app.data": {"table_name": "my.app.data", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.196, "error": null, "analysis_timestamp": "2025-07-03T16:10:39.304527"}, "firewall.paloalto": {"table_name": "firewall.paloalto", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.198, "error": null, "analysis_timestamp": "2025-07-03T16:10:39.302245"}, "my.app.tngd.actiontrailwindows": {"table_name": "my.app.tngd.actiontrailwindows", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.173, "error": null, "analysis_timestamp": "2025-07-03T16:10:39.355050"}, "my.app.tngd.adminportal": {"table_name": "my.app.tngd.adminportal", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.166, "error": null, "analysis_timestamp": "2025-07-03T16:10:39.370110"}, "my.app.tngd.cfw": {"table_name": "my.app.tngd.cfw", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.152, "error": null, "analysis_timestamp": "2025-07-03T16:10:39.404222"}, "my.app.tngd.ciscoswitch": {"table_name": "my.app.tngd.ciscoswitch", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.171, "error": null, "analysis_timestamp": "2025-07-03T16:10:39.449280"}, "my.app.tngd.cyberark": {"table_name": "my.app.tngd.cyberark", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.151, "error": null, "analysis_timestamp": "2025-07-03T16:10:39.471102"}, "my.app.tngd.ezeelogin": {"table_name": "my.app.tngd.ezeelogin", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.169, "error": null, "analysis_timestamp": "2025-07-03T16:10:39.501764"}, "my.app.tngd.h3ccoreswitch": {"table_name": "my.app.tngd.h3ccoreswitch", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.18, "error": null, "analysis_timestamp": "2025-07-03T16:10:39.502787"}, "my.app.tngd.h3cswitch": {"table_name": "my.app.tngd.h3cswitch", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.191, "error": null, "analysis_timestamp": "2025-07-03T16:10:39.503408"}, "my.app.tngd.h3cwirelessctrl": {"table_name": "my.app.tngd.h3cwirelessctrl", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.173, "error": null, "analysis_timestamp": "2025-07-03T16:10:39.529752"}, "my.app.tngd.keeper": {"table_name": "my.app.tngd.keeper", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.171, "error": null, "analysis_timestamp": "2025-07-03T16:10:39.539073"}, "my.app.tngd.polardb": {"table_name": "my.app.tngd.polardb", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.177, "error": null, "analysis_timestamp": "2025-07-03T16:10:39.557721"}, "my.app.tngd.rds": {"table_name": "my.app.tngd.rds", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.157, "error": null, "analysis_timestamp": "2025-07-03T16:10:39.622041"}, "my.app.tngd.sas": {"table_name": "my.app.tngd.sas", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.192, "error": null, "analysis_timestamp": "2025-07-03T16:10:39.623576"}, "my.app.tngd.waf": {"table_name": "my.app.tngd.waf", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.146, "error": null, "analysis_timestamp": "2025-07-03T16:10:39.671386"}, "netstat.zscaler.analyzer_zpa": {"table_name": "netstat.zscaler.analyzer_zpa", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.144, "error": null, "analysis_timestamp": "2025-07-03T16:10:39.684309"}, "nac.aruba.clearpass.session": {"table_name": "nac.aruba.clearpass.session", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.167, "error": null, "analysis_timestamp": "2025-07-03T16:10:39.697337"}, "network.switch.info": {"table_name": "network.switch.info", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.162, "error": null, "analysis_timestamp": "2025-07-03T16:10:39.704855"}, "network.cisco.router": {"table_name": "network.cisco.router", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.168, "error": null, "analysis_timestamp": "2025-07-03T16:10:39.716518"}, "siem.logtrust.alert.info": {"table_name": "siem.logtrust.alert.info", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.151, "error": null, "analysis_timestamp": "2025-07-03T16:10:39.735293"}, "siem.logtrust.auth.info": {"table_name": "siem.logtrust.auth.info", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.159, "error": null, "analysis_timestamp": "2025-07-03T16:10:39.782365"}, "siem.logtrust.dns.info": {"table_name": "siem.logtrust.dns.info", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.189, "error": null, "analysis_timestamp": "2025-07-03T16:10:39.820269"}, "siem.logtrust.network.info": {"table_name": "siem.logtrust.network.info", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.19, "error": null, "analysis_timestamp": "2025-07-03T16:10:39.821783"}, "siem.logtrust.web.activity": {"table_name": "siem.logtrust.web.activity", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.145, "error": null, "analysis_timestamp": "2025-07-03T16:10:39.893764"}, "siem.logtrust.file.info": {"table_name": "siem.logtrust.file.info", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.189, "error": null, "analysis_timestamp": "2025-07-03T16:10:39.874971"}, "siem.logtrust.process.info": {"table_name": "siem.logtrust.process.info", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.237, "error": null, "analysis_timestamp": "2025-07-03T16:10:39.835451"}, "siem.logtrust.system.info": {"table_name": "siem.logtrust.system.info", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.182, "error": null, "analysis_timestamp": "2025-07-03T16:10:39.891292"}, "siem.logtrust.registry.info": {"table_name": "siem.logtrust.registry.info", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.203, "error": null, "analysis_timestamp": "2025-07-03T16:10:39.880208"}, "system.linux.syslog": {"table_name": "system.linux.syslog", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.178, "error": null, "analysis_timestamp": "2025-07-03T16:10:39.943188"}, "system.windows.eventlog": {"table_name": "system.windows.eventlog", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.141, "error": null, "analysis_timestamp": "2025-07-03T16:10:40.017310"}, "system.auth.login": {"table_name": "system.auth.login", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.184, "error": null, "analysis_timestamp": "2025-07-03T16:10:40.016273"}, "user.activity.log": {"table_name": "user.activity.log", "success": true, "record_count": 0, "target_date": "2025-07-01", "query_duration_seconds": 0.178, "error": null, "analysis_timestamp": "2025-07-03T16:10:40.039118"}}}