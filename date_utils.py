"""
Date handling utilities for the Devo API backup system.
Provides date parsing, validation, and range generation functionality.
"""

import re
from datetime import datetime, timedelta
from typing import List, Tuple, Optional


class DateUtils:
    """Utility class for date operations."""
    
    DATE_FORMAT = "%Y-%m-%d"
    DATE_PATTERN = re.compile(r'^\d{4}-\d{2}-\d{2}$')
    
    @classmethod
    def get_current_date(cls) -> str:
        """Get current date in YYYY-MM-DD format."""
        return datetime.now().strftime(cls.DATE_FORMAT)
    
    @classmethod
    def validate_date_string(cls, date_str: str) -> bool:
        """
        Validate if a string is in YYYY-MM-DD format and represents a valid date.
        
        Args:
            date_str: Date string to validate
            
        Returns:
            True if valid, False otherwise
        """
        if not cls.DATE_PATTERN.match(date_str):
            return False
        
        try:
            datetime.strptime(date_str, cls.DATE_FORMAT)
            return True
        except ValueError:
            return False
    
    @classmethod
    def parse_date(cls, date_str: str) -> datetime:
        """
        Parse a date string into a datetime object.
        
        Args:
            date_str: Date string in YYYY-MM-DD format
            
        Returns:
            datetime object
            
        Raises:
            ValueError: If date string is invalid
        """
        if not cls.validate_date_string(date_str):
            raise ValueError(f"Invalid date format: {date_str}. Expected YYYY-MM-DD format.")
        
        return datetime.strptime(date_str, cls.DATE_FORMAT)
    
    @classmethod
    def validate_date_range(cls, start_date: str, end_date: str) -> Tuple[bool, Optional[str]]:
        """
        Validate a date range.
        
        Args:
            start_date: Start date string in YYYY-MM-DD format
            end_date: End date string in YYYY-MM-DD format
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        # Validate individual dates
        if not cls.validate_date_string(start_date):
            return False, f"Invalid start date format: {start_date}"
        
        if not cls.validate_date_string(end_date):
            return False, f"Invalid end date format: {end_date}"
        
        # Parse dates for comparison
        try:
            start_dt = cls.parse_date(start_date)
            end_dt = cls.parse_date(end_date)
        except ValueError as e:
            return False, str(e)
        
        # Check if start date is before or equal to end date
        if start_dt > end_dt:
            return False, f"Start date {start_date} must be before or equal to end date {end_date}"
        
        # Check if dates are not too far in the future
        current_date = datetime.now()
        if start_dt > current_date:
            return False, f"Start date {start_date} cannot be in the future"
        
        if end_dt > current_date:
            return False, f"End date {end_date} cannot be in the future"
        
        return True, None
    
    @classmethod
    def generate_date_range(cls, start_date: str, end_date: str) -> List[str]:
        """
        Generate a list of dates between start_date and end_date (inclusive).
        
        Args:
            start_date: Start date string in YYYY-MM-DD format
            end_date: End date string in YYYY-MM-DD format
            
        Returns:
            List of date strings in YYYY-MM-DD format
            
        Raises:
            ValueError: If date range is invalid
        """
        is_valid, error_msg = cls.validate_date_range(start_date, end_date)
        if not is_valid:
            raise ValueError(error_msg)
        
        start_dt = cls.parse_date(start_date)
        end_dt = cls.parse_date(end_date)
        
        dates = []
        current_dt = start_dt
        
        while current_dt <= end_dt:
            dates.append(current_dt.strftime(cls.DATE_FORMAT))
            current_dt += timedelta(days=1)
        
        return dates
    
    @classmethod
    def get_date_range_info(cls, start_date: str, end_date: str) -> dict:
        """
        Get information about a date range.
        
        Args:
            start_date: Start date string in YYYY-MM-DD format
            end_date: End date string in YYYY-MM-DD format
            
        Returns:
            Dictionary with range information
        """
        dates = cls.generate_date_range(start_date, end_date)
        
        return {
            'start_date': start_date,
            'end_date': end_date,
            'total_days': len(dates),
            'dates': dates
        }
    
    @classmethod
    def format_date_for_query(cls, date_str: str) -> Tuple[str, str]:
        """
        Format a date for Devo API query (start and end of day).
        
        Args:
            date_str: Date string in YYYY-MM-DD format
            
        Returns:
            Tuple of (start_datetime, end_datetime) in ISO format
        """
        date_dt = cls.parse_date(date_str)
        
        # Start of day (00:00:00)
        start_dt = date_dt.replace(hour=0, minute=0, second=0, microsecond=0)
        
        # End of day (23:59:59)
        end_dt = date_dt.replace(hour=23, minute=59, second=59, microsecond=999999)
        
        return start_dt.isoformat(), end_dt.isoformat()
