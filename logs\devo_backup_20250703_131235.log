2025-07-03 13:12:35 - devo_backup - INFO - Logging initialized. Log file: logs/devo_backup_20250703_131235.log
2025-07-03 13:12:35 - devo_backup - INFO - Specific date mode: Using date 2025-07-02
2025-07-03 13:12:35 - devo_backup - INFO - Executing specific date mode for: 2025-07-02
2025-07-03 13:12:35 - devo_backup - INFO - Devo SDK client initialized with endpoint: https://api-apac.devo.com/search/query
2025-07-03 13:12:35 - devo_backup - INFO - Starting backup for 1 date(s)
2025-07-03 13:12:35 - devo_backup - INFO - Testing connection to Devo API...
2025-07-03 13:12:35 - devo_backup - INFO - Testing connection to Devo API...
2025-07-03 13:12:35 - devo_backup - INFO - Executing Devo query: from siem.logtrust.web.activity select count() as test_count...
2025-07-03 13:12:35 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 13:12:35 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.web.activity select count() as test_count...
2025-07-03 13:12:35 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:12:35 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:12:36 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:12:36 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:12:36 - devo_backup - INFO - Devo API connection test successful
2025-07-03 13:12:36 - devo_backup - INFO - Processing date 1/1: 2025-07-02
2025-07-03 13:12:36 - devo_backup - INFO - Pulling data for date: 2025-07-02 (4 tables)
2025-07-03 13:12:36 - devo_backup - DEBUG - Querying table: firewall.all
2025-07-03 13:12:36 - devo_backup - INFO - Executing Devo query: from firewall.all...
2025-07-03 13:12:36 - devo_backup - DEBUG - Query time range: 2025-07-02T00:00:00 to 2025-07-02T23:59:59.999999
2025-07-03 13:12:36 - devo_backup - DEBUG - Executing query (attempt 1): from firewall.all...
2025-07-03 13:12:36 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:12:36 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:12:36 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:12:36 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:12:36 - devo_backup - INFO - Data pull for 2025-07-02/firewall.all: SUCCESS - Records: 0
2025-07-03 13:12:37 - devo_backup - DEBUG - Querying table: siem.logtrust.web.activity
2025-07-03 13:12:37 - devo_backup - INFO - Executing Devo query: from siem.logtrust.web.activity...
2025-07-03 13:12:37 - devo_backup - DEBUG - Query time range: 2025-07-02T00:00:00 to 2025-07-02T23:59:59.999999
2025-07-03 13:12:37 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.web.activity...
2025-07-03 13:12:37 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:12:37 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:12:37 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:12:37 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:12:37 - devo_backup - INFO - Data pull for 2025-07-02/siem.logtrust.web.activity: SUCCESS - Records: 0
2025-07-03 13:12:38 - devo_backup - DEBUG - Querying table: siem.logtrust.alert.info
2025-07-03 13:12:38 - devo_backup - INFO - Executing Devo query: from siem.logtrust.alert.info...
2025-07-03 13:12:38 - devo_backup - DEBUG - Query time range: 2025-07-02T00:00:00 to 2025-07-02T23:59:59.999999
2025-07-03 13:12:38 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.alert.info...
2025-07-03 13:12:38 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:12:38 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:12:38 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:12:38 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:12:38 - devo_backup - INFO - Data pull for 2025-07-02/siem.logtrust.alert.info: SUCCESS - Records: 0
2025-07-03 13:12:39 - devo_backup - DEBUG - Querying table: my.app.data
2025-07-03 13:12:39 - devo_backup - INFO - Executing Devo query: from my.app.data...
2025-07-03 13:12:39 - devo_backup - DEBUG - Query time range: 2025-07-02T00:00:00 to 2025-07-02T23:59:59.999999
2025-07-03 13:12:39 - devo_backup - DEBUG - Executing query (attempt 1): from my.app.data...
2025-07-03 13:12:39 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 13:12:39 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 13:12:39 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 13:12:39 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:12:39 - devo_backup - INFO - Data pull for 2025-07-02/my.app.data: SUCCESS - Records: 0
2025-07-03 13:12:40 - devo_backup - INFO - Data pull for 2025-07-02: SUCCESS - Records: 0
2025-07-03 13:12:40 - devo_backup - INFO - Completed date 2025-07-02
2025-07-03 13:12:40 - devo_backup - INFO - ============================================================
2025-07-03 13:12:40 - devo_backup - INFO - BACKUP SUMMARY
2025-07-03 13:12:40 - devo_backup - INFO - ============================================================
2025-07-03 13:12:40 - devo_backup - INFO - Total dates processed: 1
2025-07-03 13:12:40 - devo_backup - INFO - Successful dates: 1
2025-07-03 13:12:40 - devo_backup - INFO - Failed dates: 0
2025-07-03 13:12:40 - devo_backup - INFO - Total records pulled: 0
2025-07-03 13:12:40 - devo_backup - INFO - Duration: 0:00:04.726887
2025-07-03 13:12:40 - devo_backup - INFO - Start time: 2025-07-03 13:12:35.878844
2025-07-03 13:12:40 - devo_backup - INFO - End time: 2025-07-03 13:12:40.605731
2025-07-03 13:12:40 - devo_backup - INFO - Backup completed successfully!
2025-07-03 13:12:40 - devo_backup - INFO - Backup Summary - Total: 1, Success: 1, Failed: 0
2025-07-03 13:12:40 - devo_backup - INFO - Results saved to: results/backup_results_20250703_131240.json
2025-07-03 13:12:40 - devo_backup - INFO - Backup system completed successfully
