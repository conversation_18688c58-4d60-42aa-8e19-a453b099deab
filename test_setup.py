#!/usr/bin/env python3
"""
Test script to verify the Devo API backup system setup.
Run this script to check if all dependencies and configurations are correct.
"""

import sys
import os
from typing import List, Tu<PERSON>


def test_imports() -> Tuple[bool, List[str]]:
    """Test if all required modules can be imported."""
    errors = []
    
    try:
        import requests
        print("✓ requests module imported successfully")
    except ImportError as e:
        errors.append(f"Failed to import requests: {e}")
    
    try:
        from dotenv import load_dotenv
        print("✓ python-dotenv module imported successfully")
    except ImportError as e:
        errors.append(f"Failed to import python-dotenv: {e}")
    
    try:
        import urllib3
        print("✓ urllib3 module imported successfully")
    except ImportError as e:
        errors.append(f"Failed to import urllib3: {e}")
    
    return len(errors) == 0, errors


def test_project_modules() -> Tuple[bool, List[str]]:
    """Test if all project modules can be imported."""
    errors = []
    
    try:
        from config import config
        print("✓ config module imported successfully")
    except ImportError as e:
        errors.append(f"Failed to import config: {e}")
    
    try:
        from logger import logger
        print("✓ logger module imported successfully")
    except ImportError as e:
        errors.append(f"Failed to import logger: {e}")
    
    try:
        from date_utils import DateUtils
        print("✓ date_utils module imported successfully")
    except ImportError as e:
        errors.append(f"Failed to import date_utils: {e}")
    
    try:
        from devo_client import DevoClient
        print("✓ devo_client module imported successfully")
    except ImportError as e:
        errors.append(f"Failed to import devo_client: {e}")
    
    try:
        from arg_parser import parse_args
        print("✓ arg_parser module imported successfully")
    except ImportError as e:
        errors.append(f"Failed to import arg_parser: {e}")
    
    return len(errors) == 0, errors


def test_environment() -> Tuple[bool, List[str]]:
    """Test environment configuration."""
    errors = []
    
    # Check if .env file exists
    if not os.path.exists('.env'):
        errors.append(".env file not found")
        return False, errors
    
    print("✓ .env file found")
    
    try:
        from config import config
        
        # Test required environment variables
        if not config.devo_api_key:
            errors.append("DEVO_API_KEY not set or empty")
        else:
            print("✓ DEVO_API_KEY is configured")
        
        if not config.devo_api_secret:
            errors.append("DEVO_API_SECRET not set or empty")
        else:
            print("✓ DEVO_API_SECRET is configured")
        
        if not config.devo_query_endpoint:
            errors.append("DEVO_QUERY_ENDPOINT not set or empty")
        else:
            print(f"✓ DEVO_QUERY_ENDPOINT is configured: {config.devo_query_endpoint}")
        
    except Exception as e:
        errors.append(f"Error loading configuration: {e}")
    
    return len(errors) == 0, errors


def test_date_utils() -> Tuple[bool, List[str]]:
    """Test date utilities functionality."""
    errors = []
    
    try:
        from date_utils import DateUtils
        
        # Test current date
        current_date = DateUtils.get_current_date()
        if not DateUtils.validate_date_string(current_date):
            errors.append("Current date validation failed")
        else:
            print(f"✓ Current date: {current_date}")
        
        # Test date validation
        if not DateUtils.validate_date_string("2024-01-15"):
            errors.append("Date validation test failed")
        else:
            print("✓ Date validation working")
        
        # Test date range
        is_valid, _ = DateUtils.validate_date_range("2024-01-15", "2024-01-16")
        if not is_valid:
            errors.append("Date range validation test failed")
        else:
            print("✓ Date range validation working")
        
    except Exception as e:
        errors.append(f"Error testing date utilities: {e}")
    
    return len(errors) == 0, errors


def test_argument_parser() -> Tuple[bool, List[str]]:
    """Test argument parser functionality."""
    errors = []
    
    try:
        from arg_parser import ArgumentParser, ExecutionMode
        
        parser = ArgumentParser()
        
        # Test default mode
        mode, dates, errs = parser.parse_arguments([])
        if mode != ExecutionMode.DEFAULT or len(dates) != 1 or errs:
            errors.append("Default mode parsing failed")
        else:
            print("✓ Default mode parsing working")
        
        # Test specific date mode
        mode, dates, errs = parser.parse_arguments(["2024-01-15"])
        if mode != ExecutionMode.SPECIFIC_DATE or dates != ["2024-01-15"] or errs:
            errors.append("Specific date mode parsing failed")
        else:
            print("✓ Specific date mode parsing working")
        
        # Test date range mode
        mode, dates, errs = parser.parse_arguments(["2024-01-15", "2024-01-16"])
        if mode != ExecutionMode.DATE_RANGE or len(dates) != 2 or errs:
            errors.append("Date range mode parsing failed")
        else:
            print("✓ Date range mode parsing working")
        
    except Exception as e:
        errors.append(f"Error testing argument parser: {e}")
    
    return len(errors) == 0, errors


def main():
    """Run all tests."""
    print("Devo API Backup System - Setup Test")
    print("=" * 50)
    
    all_passed = True
    
    # Test imports
    print("\n1. Testing Python dependencies...")
    passed, errors = test_imports()
    if not passed:
        all_passed = False
        for error in errors:
            print(f"✗ {error}")
    
    # Test project modules
    print("\n2. Testing project modules...")
    passed, errors = test_project_modules()
    if not passed:
        all_passed = False
        for error in errors:
            print(f"✗ {error}")
    
    # Test environment
    print("\n3. Testing environment configuration...")
    passed, errors = test_environment()
    if not passed:
        all_passed = False
        for error in errors:
            print(f"✗ {error}")
    
    # Test date utils
    print("\n4. Testing date utilities...")
    passed, errors = test_date_utils()
    if not passed:
        all_passed = False
        for error in errors:
            print(f"✗ {error}")
    
    # Test argument parser
    print("\n5. Testing argument parser...")
    passed, errors = test_argument_parser()
    if not passed:
        all_passed = False
        for error in errors:
            print(f"✗ {error}")
    
    # Summary
    print("\n" + "=" * 50)
    if all_passed:
        print("✓ All tests passed! The system is ready to use.")
        print("\nNext steps:")
        print("1. Run: python main.py --help")
        print("2. Test with: python main.py")
        sys.exit(0)
    else:
        print("✗ Some tests failed. Please fix the issues above.")
        print("\nCommon solutions:")
        print("1. Install dependencies: pip install -r requirements.txt")
        print("2. Check your .env file configuration")
        print("3. Ensure all project files are in the same directory")
        sys.exit(1)


if __name__ == "__main__":
    main()
