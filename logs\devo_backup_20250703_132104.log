2025-07-03 13:21:04 - devo_backup - INFO - Logging initialized. Log file: logs/devo_backup_20250703_132104.log
2025-07-03 13:21:04 - devo_backup - INFO - Devo SDK client initialized with endpoint: https://api-apac.devo.com/search/query
2025-07-03 13:21:04 - devo_backup - INFO - Testing connection to Devo API...
2025-07-03 13:21:04 - devo_backup - INFO - Testing connection to Devo API...
2025-07-03 13:21:04 - devo_backup - INFO - Executing Devo query: from siem.logtrust.web.activity select count() as test_count...
2025-07-03 13:21:04 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:04 - devo_backup - INFO - Devo API connection test successful
2025-07-03 13:21:04 - devo_backup - INFO - Attempting to discover tables via system metadata...
2025-07-03 13:21:04 - devo_backup - INFO - Executing Devo query: from siem.logtrust.table.info select table...
2025-07-03 13:21:04 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:04 - devo_backup - INFO - Executing Devo query: from system.table.metadata select table_name...
2025-07-03 13:21:04 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:04 - devo_backup - INFO - Executing Devo query: from devo.system.tables select name...
2025-07-03 13:21:05 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:05 - devo_backup - INFO - Executing Devo query: show tables...
2025-07-03 13:21:05 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:05 - devo_backup - INFO - Executing Devo query: describe tables...
2025-07-03 13:21:05 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:05 - devo_backup - INFO - Scanning common table patterns...
2025-07-03 13:21:05 - devo_backup - INFO - Testing 28 common table patterns...
2025-07-03 13:21:05 - devo_backup - INFO - Executing Devo query: from siem.logtrust.web.activity select count() as record_count...
2025-07-03 13:21:05 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:05 - devo_backup - INFO - Executing Devo query: from siem.logtrust.alert.info select count() as record_count...
2025-07-03 13:21:05 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:05 - devo_backup - INFO - Executing Devo query: from siem.logtrust.auth.info select count() as record_count...
2025-07-03 13:21:06 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:06 - devo_backup - INFO - Executing Devo query: from siem.logtrust.dns.info select count() as record_count...
2025-07-03 13:21:06 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:06 - devo_backup - INFO - Executing Devo query: from siem.logtrust.file.info select count() as record_count...
2025-07-03 13:21:06 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:06 - devo_backup - INFO - Executing Devo query: from siem.logtrust.network.info select count() as record_count...
2025-07-03 13:21:06 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:06 - devo_backup - INFO - Executing Devo query: from siem.logtrust.process.info select count() as record_count...
2025-07-03 13:21:07 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:07 - devo_backup - INFO - Executing Devo query: from siem.logtrust.registry.info select count() as record_count...
2025-07-03 13:21:07 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:07 - devo_backup - INFO - Executing Devo query: from siem.logtrust.system.info select count() as record_count...
2025-07-03 13:21:07 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:07 - devo_backup - INFO - Executing Devo query: from firewall.all select count() as record_count...
2025-07-03 13:21:08 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:08 - devo_backup - INFO - Executing Devo query: from firewall.cisco.asa select count() as record_count...
2025-07-03 13:21:08 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:08 - devo_backup - INFO - Executing Devo query: from firewall.paloalto select count() as record_count...
2025-07-03 13:21:08 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:08 - devo_backup - INFO - Executing Devo query: from firewall.fortinet select count() as record_count...
2025-07-03 13:21:09 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:09 - devo_backup - INFO - Executing Devo query: from network.cisco.router select count() as record_count...
2025-07-03 13:21:09 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:09 - devo_backup - INFO - Executing Devo query: from network.switch.info select count() as record_count...
2025-07-03 13:21:09 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:09 - devo_backup - INFO - Executing Devo query: from app.web.access select count() as record_count...
2025-07-03 13:21:09 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:09 - devo_backup - INFO - Executing Devo query: from app.web.error select count() as record_count...
2025-07-03 13:21:10 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:10 - devo_backup - INFO - Executing Devo query: from app.database.audit select count() as record_count...
2025-07-03 13:21:10 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:10 - devo_backup - INFO - Executing Devo query: from app.api.requests select count() as record_count...
2025-07-03 13:21:10 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:10 - devo_backup - INFO - Executing Devo query: from system.windows.eventlog select count() as record_count...
2025-07-03 13:21:10 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:10 - devo_backup - INFO - Executing Devo query: from system.linux.syslog select count() as record_count...
2025-07-03 13:21:11 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:11 - devo_backup - INFO - Executing Devo query: from system.auth.login select count() as record_count...
2025-07-03 13:21:11 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:11 - devo_backup - INFO - Executing Devo query: from cloud.aws.cloudtrail select count() as record_count...
2025-07-03 13:21:11 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:11 - devo_backup - INFO - Executing Devo query: from cloud.azure.activity select count() as record_count...
2025-07-03 13:21:11 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:11 - devo_backup - INFO - Executing Devo query: from cloud.gcp.audit select count() as record_count...
2025-07-03 13:21:12 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:12 - devo_backup - INFO - Executing Devo query: from my.app.data select count() as record_count...
2025-07-03 13:21:12 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:12 - devo_backup - INFO - Executing Devo query: from custom.logs.info select count() as record_count...
2025-07-03 13:21:12 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:12 - devo_backup - INFO - Executing Devo query: from user.activity.log select count() as record_count...
2025-07-03 13:21:12 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:12 - devo_backup - INFO - Found 28 tables via pattern scanning
2025-07-03 13:21:12 - devo_backup - INFO - Found 28 additional tables via pattern scanning
2025-07-03 13:21:12 - devo_backup - INFO - Scanning known prefixes...
2025-07-03 13:21:12 - devo_backup - INFO - Scanning 15 known prefixes...
2025-07-03 13:21:12 - devo_backup - INFO - Total unique tables discovered: 28
2025-07-03 13:21:12 - devo_backup - INFO - Executing Devo query: from app.api.requests select count() as record_count...
2025-07-03 13:21:13 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:13 - devo_backup - INFO - Executing Devo query: from app.api.requests select * limit 5...
2025-07-03 13:21:13 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:13 - devo_backup - INFO - Executing Devo query: from app.api.requests select * limit 3...
2025-07-03 13:21:13 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:13 - devo_backup - INFO - Executing Devo query: from app.api.requests select max(timestamp) as last_update...
2025-07-03 13:21:13 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:13 - devo_backup - INFO - Executing Devo query: from app.api.requests select max(eventdate) as last_update...
2025-07-03 13:21:13 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:13 - devo_backup - INFO - Executing Devo query: from app.api.requests select max(date) as last_update...
2025-07-03 13:21:13 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:13 - devo_backup - INFO - Executing Devo query: from app.api.requests select max(time) as last_update...
2025-07-03 13:21:14 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:14 - devo_backup - INFO - Executing Devo query: from app.api.requests select max(_time) as last_update...
2025-07-03 13:21:14 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:14 - devo_backup - INFO - Executing Devo query: from app.api.requests select max(created_at) as last_update...
2025-07-03 13:21:14 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:14 - devo_backup - INFO - Executing Devo query: from app.api.requests select max(updated_at) as last_update...
2025-07-03 13:21:14 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:14 - devo_backup - INFO - Executing Devo query: from app.api.requests select count() as record_count...
2025-07-03 13:21:14 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:14 - devo_backup - INFO - Executing Devo query: from app.api.requests select * limit 5...
2025-07-03 13:21:15 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:15 - devo_backup - INFO - Executing Devo query: from app.api.requests select * limit 3...
2025-07-03 13:21:15 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:15 - devo_backup - INFO - Executing Devo query: from app.api.requests select max(timestamp) as last_update...
2025-07-03 13:21:15 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:15 - devo_backup - INFO - Executing Devo query: from app.api.requests select max(eventdate) as last_update...
2025-07-03 13:21:15 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:15 - devo_backup - INFO - Executing Devo query: from app.api.requests select max(date) as last_update...
2025-07-03 13:21:15 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:15 - devo_backup - INFO - Executing Devo query: from app.api.requests select max(time) as last_update...
2025-07-03 13:21:15 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:15 - devo_backup - INFO - Executing Devo query: from app.api.requests select max(_time) as last_update...
2025-07-03 13:21:16 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:16 - devo_backup - INFO - Executing Devo query: from app.api.requests select max(created_at) as last_update...
2025-07-03 13:21:16 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:16 - devo_backup - INFO - Executing Devo query: from app.api.requests select max(updated_at) as last_update...
2025-07-03 13:21:16 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:16 - devo_backup - INFO - Discovery results saved to: results\test_discovery_results.json
2025-07-03 13:21:16 - devo_backup - INFO - Devo SDK client initialized with endpoint: https://api-apac.devo.com/search/query
2025-07-03 13:21:16 - devo_backup - INFO - Testing 28 common table patterns...
2025-07-03 13:21:16 - devo_backup - INFO - Executing Devo query: from siem.logtrust.web.activity select count() as record_count...
2025-07-03 13:21:16 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:16 - devo_backup - INFO - Executing Devo query: from siem.logtrust.alert.info select count() as record_count...
2025-07-03 13:21:16 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:16 - devo_backup - INFO - Executing Devo query: from siem.logtrust.auth.info select count() as record_count...
2025-07-03 13:21:17 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:17 - devo_backup - INFO - Executing Devo query: from siem.logtrust.dns.info select count() as record_count...
2025-07-03 13:21:17 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:17 - devo_backup - INFO - Executing Devo query: from siem.logtrust.file.info select count() as record_count...
2025-07-03 13:21:17 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:17 - devo_backup - INFO - Executing Devo query: from siem.logtrust.network.info select count() as record_count...
2025-07-03 13:21:17 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:17 - devo_backup - INFO - Executing Devo query: from siem.logtrust.process.info select count() as record_count...
2025-07-03 13:21:18 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:18 - devo_backup - INFO - Executing Devo query: from siem.logtrust.registry.info select count() as record_count...
2025-07-03 13:21:18 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:18 - devo_backup - INFO - Executing Devo query: from siem.logtrust.system.info select count() as record_count...
2025-07-03 13:21:18 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:18 - devo_backup - INFO - Executing Devo query: from firewall.all select count() as record_count...
2025-07-03 13:21:18 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:18 - devo_backup - INFO - Executing Devo query: from firewall.cisco.asa select count() as record_count...
2025-07-03 13:21:19 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:19 - devo_backup - INFO - Executing Devo query: from firewall.paloalto select count() as record_count...
2025-07-03 13:21:19 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:19 - devo_backup - INFO - Executing Devo query: from firewall.fortinet select count() as record_count...
2025-07-03 13:21:19 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:19 - devo_backup - INFO - Executing Devo query: from network.cisco.router select count() as record_count...
2025-07-03 13:21:19 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:19 - devo_backup - INFO - Executing Devo query: from network.switch.info select count() as record_count...
2025-07-03 13:21:19 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:20 - devo_backup - INFO - Executing Devo query: from app.web.access select count() as record_count...
2025-07-03 13:21:20 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:20 - devo_backup - INFO - Executing Devo query: from app.web.error select count() as record_count...
2025-07-03 13:21:20 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:20 - devo_backup - INFO - Executing Devo query: from app.database.audit select count() as record_count...
2025-07-03 13:21:20 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:20 - devo_backup - INFO - Executing Devo query: from app.api.requests select count() as record_count...
2025-07-03 13:21:20 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:21 - devo_backup - INFO - Executing Devo query: from system.windows.eventlog select count() as record_count...
2025-07-03 13:21:21 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:21 - devo_backup - INFO - Executing Devo query: from system.linux.syslog select count() as record_count...
2025-07-03 13:21:21 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:21 - devo_backup - INFO - Executing Devo query: from system.auth.login select count() as record_count...
2025-07-03 13:21:21 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:21 - devo_backup - INFO - Executing Devo query: from cloud.aws.cloudtrail select count() as record_count...
2025-07-03 13:21:21 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:21 - devo_backup - INFO - Executing Devo query: from cloud.azure.activity select count() as record_count...
2025-07-03 13:21:22 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:22 - devo_backup - INFO - Executing Devo query: from cloud.gcp.audit select count() as record_count...
2025-07-03 13:21:22 - devo_backup - INFO - Query returned 0 records
2025-07-03 13:21:22 - devo_backup - INFO - Executing Devo query: from my.app.data select count() as record_count...
