2025-07-03 16:10:37 - devo_backup - INFO - Logging initialized. Log file: logs/devo_backup_20250703_161037.log
2025-07-03 16:10:37 - devo_backup - INFO - Starting Devo Data Volume Analysis...
2025-07-03 16:10:37 - devo_backup - INFO - Target date: 2025-07-01
2025-07-03 16:10:37 - devo_backup - INFO - Devo endpoint: https://api-apac.devo.com/search/query
2025-07-03 16:10:37 - devo_backup - INFO - Devo SDK client initialized with endpoint: https://api-apac.devo.com/search/query
2025-07-03 16:10:37 - devo_backup - INFO - Loading discovered tables...
2025-07-03 16:10:37 - devo_backup - INFO - Using latest discovery results: results\table_discovery_results_20250703_160303.json
2025-07-03 16:10:37 - devo_backup - INFO - Loaded 91 tables from discovery results
2025-07-03 16:10:37 - devo_backup - INFO - Starting data volume analysis for 91 tables on 2025-07-01
2025-07-03 16:10:37 - devo_backup - INFO - Testing connection to Devo API...
2025-07-03 16:10:37 - devo_backup - INFO - Testing connection to Devo API...
2025-07-03 16:10:37 - devo_backup - INFO - Executing Devo query: from siem.logtrust.web.activity select count() as test_count...
2025-07-03 16:10:37 - devo_backup - DEBUG - Query time range: 2025-07-03T00:00:00 to 2025-07-03T23:59:59.999999
2025-07-03 16:10:37 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.web.activity select count() as test_count...
2025-07-03 16:10:37 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:37 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:38 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:38 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:38 - devo_backup - INFO - Devo API connection test successful
2025-07-03 16:10:38 - devo_backup - INFO - Analyzing table data volumes...
2025-07-03 16:10:38 - devo_backup - INFO - Starting parallel analysis with 8 workers...
2025-07-03 16:10:38 - devo_backup - DEBUG - Analyzing data volume for table: app.lark.audit.event
2025-07-03 16:10:38 - devo_backup - DEBUG - Analyzing data volume for table: app.web.error
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing count query for app.lark.audit.event: from app.lark.audit.event select count() as record_count
2025-07-03 16:10:38 - devo_backup - DEBUG - Analyzing data volume for table: app.api.requests
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing count query for app.web.error: from app.web.error select count() as record_count
2025-07-03 16:10:38 - devo_backup - DEBUG - Analyzing data volume for table: app.web.access
2025-07-03 16:10:38 - devo_backup - INFO - Executing Devo query: from app.lark.audit.event select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing count query for app.api.requests: from app.api.requests select count() as record_count
2025-07-03 16:10:38 - devo_backup - DEBUG - Analyzing data volume for table: app.database.audit
2025-07-03 16:10:38 - devo_backup - INFO - Executing Devo query: from app.web.error select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Analyzing data volume for table: box.stat.unix.diskstat
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing count query for app.web.access: from app.web.access select count() as record_count
2025-07-03 16:10:38 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:38 - devo_backup - INFO - Executing Devo query: from app.api.requests select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Analyzing data volume for table: cef0.zscaler.nssweblog
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing count query for app.database.audit: from app.database.audit select count() as record_count
2025-07-03 16:10:38 - devo_backup - DEBUG - Analyzing data volume for table: cloud.alibaba.log_service.events
2025-07-03 16:10:38 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing count query for box.stat.unix.diskstat: from box.stat.unix.diskstat select count() as record_count
2025-07-03 16:10:38 - devo_backup - INFO - Executing Devo query: from app.web.access select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing query (attempt 1): from app.lark.audit.event select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing count query for cef0.zscaler.nssweblog: from cef0.zscaler.nssweblog select count() as record_count
2025-07-03 16:10:38 - devo_backup - INFO - Executing Devo query: from app.database.audit select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing count query for cloud.alibaba.log_service.events: from cloud.alibaba.log_service.events select count() as record_count
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing query (attempt 1): from app.web.error select count() as record_count...
2025-07-03 16:10:38 - devo_backup - INFO - Executing Devo query: from box.stat.unix.diskstat select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:38 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing query (attempt 1): from app.api.requests select count() as record_count...
2025-07-03 16:10:38 - devo_backup - INFO - Executing Devo query: from cef0.zscaler.nssweblog select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:38 - devo_backup - INFO - Executing Devo query: from cloud.alibaba.log_service.events select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:38 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing query (attempt 1): from app.web.access select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:38 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:38 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing query (attempt 1): from app.database.audit select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:38 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing query (attempt 1): from box.stat.unix.diskstat select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:38 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing query (attempt 1): from cef0.zscaler.nssweblog select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing query (attempt 1): from cloud.alibaba.log_service.events select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:38 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:38 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:38 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:38 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:38 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:38 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:38 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:38 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:38 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:38 - devo_backup - DEBUG - Successfully analyzed app.web.error: 0 records
2025-07-03 16:10:38 - devo_backup - DEBUG - Analyzing data volume for table: cloud.aws.cloudtrail
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing count query for cloud.aws.cloudtrail: from cloud.aws.cloudtrail select count() as record_count
2025-07-03 16:10:38 - devo_backup - INFO - Executing Devo query: from cloud.aws.cloudtrail select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing query (attempt 1): from cloud.aws.cloudtrail select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:38 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:38 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:38 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:38 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:38 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:38 - devo_backup - DEBUG - Successfully analyzed app.api.requests: 0 records
2025-07-03 16:10:38 - devo_backup - DEBUG - Successfully analyzed app.lark.audit.event: 0 records
2025-07-03 16:10:38 - devo_backup - DEBUG - Analyzing data volume for table: cloud.azure.activity
2025-07-03 16:10:38 - devo_backup - DEBUG - Analyzing data volume for table: cloud.gcp.audit
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing count query for cloud.azure.activity: from cloud.azure.activity select count() as record_count
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing count query for cloud.gcp.audit: from cloud.gcp.audit select count() as record_count
2025-07-03 16:10:38 - devo_backup - INFO - Executing Devo query: from cloud.azure.activity select count() as record_count...
2025-07-03 16:10:38 - devo_backup - INFO - Executing Devo query: from cloud.gcp.audit select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:38 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing query (attempt 1): from cloud.azure.activity select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing query (attempt 1): from cloud.gcp.audit select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:38 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:38 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:38 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:38 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:38 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:38 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:38 - devo_backup - DEBUG - Successfully analyzed app.web.access: 0 records
2025-07-03 16:10:38 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:38 - devo_backup - DEBUG - Analyzing data volume for table: cloud.office365.management.airinvestigation
2025-07-03 16:10:38 - devo_backup - DEBUG - Successfully analyzed cloud.alibaba.log_service.events: 0 records
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing count query for cloud.office365.management.airinvestigation: from cloud.office365.management.airinvestigation select count() as record_count
2025-07-03 16:10:38 - devo_backup - DEBUG - Analyzing data volume for table: cloud.office365.management.azureactivedirectory
2025-07-03 16:10:38 - devo_backup - INFO - Executing Devo query: from cloud.office365.management.airinvestigation select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing count query for cloud.office365.management.azureactivedirectory: from cloud.office365.management.azureactivedirectory select count() as record_count
2025-07-03 16:10:38 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:38 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:38 - devo_backup - INFO - Executing Devo query: from cloud.office365.management.azureactivedirectory select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing query (attempt 1): from cloud.office365.management.airinvestigation select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Successfully analyzed cef0.zscaler.nssweblog: 0 records
2025-07-03 16:10:38 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:38 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:38 - devo_backup - DEBUG - Analyzing data volume for table: cloud.office365.management.complianceposturemanagement
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing count query for cloud.office365.management.complianceposturemanagement: from cloud.office365.management.complianceposturemanagement select count() as record_count
2025-07-03 16:10:38 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing query (attempt 1): from cloud.office365.management.azureactivedirectory select count() as record_count...
2025-07-03 16:10:38 - devo_backup - INFO - Executing Devo query: from cloud.office365.management.complianceposturemanagement select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:38 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:38 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing query (attempt 1): from cloud.office365.management.complianceposturemanagement select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:38 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:38 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:38 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:38 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:38 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:38 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:38 - devo_backup - DEBUG - Successfully analyzed box.stat.unix.diskstat: 0 records
2025-07-03 16:10:38 - devo_backup - DEBUG - Successfully analyzed app.database.audit: 0 records
2025-07-03 16:10:38 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:38 - devo_backup - DEBUG - Analyzing data volume for table: cloud.office365.management.copilot
2025-07-03 16:10:38 - devo_backup - DEBUG - Analyzing data volume for table: cloud.office365.management.endpoint
2025-07-03 16:10:38 - devo_backup - DEBUG - Successfully analyzed cloud.aws.cloudtrail: 0 records
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing count query for cloud.office365.management.copilot: from cloud.office365.management.copilot select count() as record_count
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing count query for cloud.office365.management.endpoint: from cloud.office365.management.endpoint select count() as record_count
2025-07-03 16:10:38 - devo_backup - DEBUG - Analyzing data volume for table: cloud.office365.management.microsoftflow
2025-07-03 16:10:38 - devo_backup - INFO - Executing Devo query: from cloud.office365.management.copilot select count() as record_count...
2025-07-03 16:10:38 - devo_backup - INFO - Executing Devo query: from cloud.office365.management.endpoint select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing count query for cloud.office365.management.microsoftflow: from cloud.office365.management.microsoftflow select count() as record_count
2025-07-03 16:10:38 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:38 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:38 - devo_backup - INFO - Executing Devo query: from cloud.office365.management.microsoftflow select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing query (attempt 1): from cloud.office365.management.copilot select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing query (attempt 1): from cloud.office365.management.endpoint select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:38 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:38 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing query (attempt 1): from cloud.office365.management.microsoftflow select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:38 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:38 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:38 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:38 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:38 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:38 - devo_backup - DEBUG - Successfully analyzed cloud.azure.activity: 0 records
2025-07-03 16:10:38 - devo_backup - DEBUG - Analyzing data volume for table: cloud.office365.management.exchange
2025-07-03 16:10:38 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:38 - devo_backup - INFO - Progress: 10/91 tables analyzed
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing count query for cloud.office365.management.exchange: from cloud.office365.management.exchange select count() as record_count
2025-07-03 16:10:38 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:38 - devo_backup - INFO - Executing Devo query: from cloud.office365.management.exchange select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Successfully analyzed cloud.gcp.audit: 0 records
2025-07-03 16:10:38 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:38 - devo_backup - DEBUG - Analyzing data volume for table: cloud.office365.management.microsoftforms
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing query (attempt 1): from cloud.office365.management.exchange select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing count query for cloud.office365.management.microsoftforms: from cloud.office365.management.microsoftforms select count() as record_count
2025-07-03 16:10:38 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:38 - devo_backup - INFO - Executing Devo query: from cloud.office365.management.microsoftforms select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:38 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing query (attempt 1): from cloud.office365.management.microsoftforms select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:38 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:38 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:38 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:38 - devo_backup - DEBUG - Successfully analyzed cloud.office365.management.airinvestigation: 0 records
2025-07-03 16:10:38 - devo_backup - DEBUG - Analyzing data volume for table: cloud.office365.management.microsoftteams
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing count query for cloud.office365.management.microsoftteams: from cloud.office365.management.microsoftteams select count() as record_count
2025-07-03 16:10:38 - devo_backup - INFO - Executing Devo query: from cloud.office365.management.microsoftteams select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing query (attempt 1): from cloud.office365.management.microsoftteams select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:38 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:38 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:38 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:38 - devo_backup - DEBUG - Successfully analyzed cloud.office365.management.azureactivedirectory: 0 records
2025-07-03 16:10:38 - devo_backup - DEBUG - Analyzing data volume for table: cloud.office365.management.microsofttodo
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing count query for cloud.office365.management.microsofttodo: from cloud.office365.management.microsofttodo select count() as record_count
2025-07-03 16:10:38 - devo_backup - INFO - Executing Devo query: from cloud.office365.management.microsofttodo select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing query (attempt 1): from cloud.office365.management.microsofttodo select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:38 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:38 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:38 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:38 - devo_backup - DEBUG - Successfully analyzed cloud.office365.management.complianceposturemanagement: 0 records
2025-07-03 16:10:38 - devo_backup - DEBUG - Analyzing data volume for table: cloud.office365.management.onedrive
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing count query for cloud.office365.management.onedrive: from cloud.office365.management.onedrive select count() as record_count
2025-07-03 16:10:38 - devo_backup - INFO - Executing Devo query: from cloud.office365.management.onedrive select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing query (attempt 1): from cloud.office365.management.onedrive select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:38 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:38 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:38 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:38 - devo_backup - DEBUG - Successfully analyzed cloud.office365.management.microsoftflow: 0 records
2025-07-03 16:10:38 - devo_backup - DEBUG - Analyzing data volume for table: cloud.office365.management.powerbi
2025-07-03 16:10:38 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing count query for cloud.office365.management.powerbi: from cloud.office365.management.powerbi select count() as record_count
2025-07-03 16:10:38 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:38 - devo_backup - INFO - Executing Devo query: from cloud.office365.management.powerbi select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Successfully analyzed cloud.office365.management.endpoint: 0 records
2025-07-03 16:10:38 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:38 - devo_backup - DEBUG - Analyzing data volume for table: cloud.office365.management.planner
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing query (attempt 1): from cloud.office365.management.powerbi select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing count query for cloud.office365.management.planner: from cloud.office365.management.planner select count() as record_count
2025-07-03 16:10:38 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:38 - devo_backup - INFO - Executing Devo query: from cloud.office365.management.planner select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:38 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing query (attempt 1): from cloud.office365.management.planner select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:38 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:38 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:38 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:38 - devo_backup - DEBUG - Successfully analyzed cloud.office365.management.copilot: 0 records
2025-07-03 16:10:38 - devo_backup - DEBUG - Analyzing data volume for table: cloud.office365.management.powerplatform
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing count query for cloud.office365.management.powerplatform: from cloud.office365.management.powerplatform select count() as record_count
2025-07-03 16:10:38 - devo_backup - INFO - Executing Devo query: from cloud.office365.management.powerplatform select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing query (attempt 1): from cloud.office365.management.powerplatform select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:38 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:38 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:38 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:38 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:38 - devo_backup - DEBUG - Successfully analyzed cloud.office365.management.exchange: 0 records
2025-07-03 16:10:38 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:38 - devo_backup - DEBUG - Analyzing data volume for table: cloud.office365.management.quarantine
2025-07-03 16:10:38 - devo_backup - DEBUG - Successfully analyzed cloud.office365.management.microsoftforms: 0 records
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing count query for cloud.office365.management.quarantine: from cloud.office365.management.quarantine select count() as record_count
2025-07-03 16:10:38 - devo_backup - DEBUG - Analyzing data volume for table: cloud.office365.management.publicendpoint
2025-07-03 16:10:38 - devo_backup - INFO - Executing Devo query: from cloud.office365.management.quarantine select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing count query for cloud.office365.management.publicendpoint: from cloud.office365.management.publicendpoint select count() as record_count
2025-07-03 16:10:38 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:38 - devo_backup - INFO - Executing Devo query: from cloud.office365.management.publicendpoint select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing query (attempt 1): from cloud.office365.management.quarantine select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:38 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing query (attempt 1): from cloud.office365.management.publicendpoint select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:38 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:38 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:38 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:38 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:38 - devo_backup - DEBUG - Successfully analyzed cloud.office365.management.microsoftteams: 0 records
2025-07-03 16:10:38 - devo_backup - DEBUG - Analyzing data volume for table: cloud.office365.management.securitycompliancecenter
2025-07-03 16:10:38 - devo_backup - INFO - Progress: 20/91 tables analyzed
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing count query for cloud.office365.management.securitycompliancecenter: from cloud.office365.management.securitycompliancecenter select count() as record_count
2025-07-03 16:10:38 - devo_backup - INFO - Executing Devo query: from cloud.office365.management.securitycompliancecenter select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing query (attempt 1): from cloud.office365.management.securitycompliancecenter select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:38 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:38 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:38 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:38 - devo_backup - DEBUG - Successfully analyzed cloud.office365.management.onedrive: 0 records
2025-07-03 16:10:38 - devo_backup - DEBUG - Analyzing data volume for table: cloud.office365.management.sharepoint
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing count query for cloud.office365.management.sharepoint: from cloud.office365.management.sharepoint select count() as record_count
2025-07-03 16:10:38 - devo_backup - INFO - Executing Devo query: from cloud.office365.management.sharepoint select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing query (attempt 1): from cloud.office365.management.sharepoint select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:38 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:38 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:38 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:38 - devo_backup - DEBUG - Successfully analyzed cloud.office365.management.microsofttodo: 0 records
2025-07-03 16:10:38 - devo_backup - DEBUG - Analyzing data volume for table: cloud.office365.management.threatintelligence
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing count query for cloud.office365.management.threatintelligence: from cloud.office365.management.threatintelligence select count() as record_count
2025-07-03 16:10:38 - devo_backup - INFO - Executing Devo query: from cloud.office365.management.threatintelligence select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing query (attempt 1): from cloud.office365.management.threatintelligence select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:38 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:38 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:38 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:38 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:38 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:38 - devo_backup - DEBUG - Successfully analyzed cloud.office365.management.planner: 0 records
2025-07-03 16:10:38 - devo_backup - DEBUG - Successfully analyzed cloud.office365.management.powerplatform: 0 records
2025-07-03 16:10:38 - devo_backup - DEBUG - Analyzing data volume for table: cloud.office365.management.workplaceanalytics
2025-07-03 16:10:38 - devo_backup - DEBUG - Analyzing data volume for table: cloud.office365.management.yammer
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing count query for cloud.office365.management.workplaceanalytics: from cloud.office365.management.workplaceanalytics select count() as record_count
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing count query for cloud.office365.management.yammer: from cloud.office365.management.yammer select count() as record_count
2025-07-03 16:10:38 - devo_backup - INFO - Executing Devo query: from cloud.office365.management.workplaceanalytics select count() as record_count...
2025-07-03 16:10:38 - devo_backup - INFO - Executing Devo query: from cloud.office365.management.yammer select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:38 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:38 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing query (attempt 1): from cloud.office365.management.workplaceanalytics select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing query (attempt 1): from cloud.office365.management.yammer select count() as record_count...
2025-07-03 16:10:38 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:38 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:38 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:38 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:38 - devo_backup - DEBUG - Successfully analyzed cloud.office365.management.powerbi: 0 records
2025-07-03 16:10:38 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:38 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:38 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:38 - devo_backup - DEBUG - Analyzing data volume for table: custom.logs.info
2025-07-03 16:10:38 - devo_backup - DEBUG - Successfully analyzed cloud.office365.management.publicendpoint: 0 records
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing count query for custom.logs.info: from custom.logs.info select count() as record_count
2025-07-03 16:10:38 - devo_backup - DEBUG - Analyzing data volume for table: edr.crowdstrike.falconstreaming.agents
2025-07-03 16:10:38 - devo_backup - INFO - Executing Devo query: from custom.logs.info select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing count query for edr.crowdstrike.falconstreaming.agents: from edr.crowdstrike.falconstreaming.agents select count() as record_count
2025-07-03 16:10:38 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:38 - devo_backup - INFO - Executing Devo query: from edr.crowdstrike.falconstreaming.agents select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing query (attempt 1): from custom.logs.info select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:38 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing query (attempt 1): from edr.crowdstrike.falconstreaming.agents select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:38 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:38 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:38 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:38 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:38 - devo_backup - DEBUG - Successfully analyzed cloud.office365.management.sharepoint: 0 records
2025-07-03 16:10:38 - devo_backup - DEBUG - Analyzing data volume for table: edr.crowdstrike.falconstreaming.alert
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing count query for edr.crowdstrike.falconstreaming.alert: from edr.crowdstrike.falconstreaming.alert select count() as record_count
2025-07-03 16:10:38 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:38 - devo_backup - INFO - Executing Devo query: from edr.crowdstrike.falconstreaming.alert select count() as record_count...
2025-07-03 16:10:38 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:38 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:38 - devo_backup - DEBUG - Successfully analyzed cloud.office365.management.securitycompliancecenter: 0 records
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing query (attempt 1): from edr.crowdstrike.falconstreaming.alert select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:38 - devo_backup - DEBUG - Analyzing data volume for table: edr.crowdstrike.falconstreaming.auth_activity
2025-07-03 16:10:38 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing count query for edr.crowdstrike.falconstreaming.auth_activity: from edr.crowdstrike.falconstreaming.auth_activity select count() as record_count
2025-07-03 16:10:38 - devo_backup - INFO - Executing Devo query: from edr.crowdstrike.falconstreaming.auth_activity select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing query (attempt 1): from edr.crowdstrike.falconstreaming.auth_activity select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:38 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:38 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:38 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:38 - devo_backup - DEBUG - Successfully analyzed cloud.office365.management.quarantine: 0 records
2025-07-03 16:10:38 - devo_backup - DEBUG - Analyzing data volume for table: edr.crowdstrike.falconstreaming.behaviors
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing count query for edr.crowdstrike.falconstreaming.behaviors: from edr.crowdstrike.falconstreaming.behaviors select count() as record_count
2025-07-03 16:10:38 - devo_backup - INFO - Executing Devo query: from edr.crowdstrike.falconstreaming.behaviors select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing query (attempt 1): from edr.crowdstrike.falconstreaming.behaviors select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:38 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:38 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:38 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:38 - devo_backup - DEBUG - Successfully analyzed cloud.office365.management.threatintelligence: 0 records
2025-07-03 16:10:38 - devo_backup - DEBUG - Analyzing data volume for table: edr.crowdstrike.falconstreaming.detection_summary
2025-07-03 16:10:38 - devo_backup - INFO - Progress: 30/91 tables analyzed
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing count query for edr.crowdstrike.falconstreaming.detection_summary: from edr.crowdstrike.falconstreaming.detection_summary select count() as record_count
2025-07-03 16:10:38 - devo_backup - INFO - Executing Devo query: from edr.crowdstrike.falconstreaming.detection_summary select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing query (attempt 1): from edr.crowdstrike.falconstreaming.detection_summary select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:38 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:38 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:38 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:38 - devo_backup - DEBUG - Successfully analyzed cloud.office365.management.yammer: 0 records
2025-07-03 16:10:38 - devo_backup - DEBUG - Analyzing data volume for table: edr.crowdstrike.falconstreaming.epp_detection_summary
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing count query for edr.crowdstrike.falconstreaming.epp_detection_summary: from edr.crowdstrike.falconstreaming.epp_detection_summary select count() as record_count
2025-07-03 16:10:38 - devo_backup - INFO - Executing Devo query: from edr.crowdstrike.falconstreaming.epp_detection_summary select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing query (attempt 1): from edr.crowdstrike.falconstreaming.epp_detection_summary select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:38 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:38 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:38 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:38 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:38 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:38 - devo_backup - DEBUG - Successfully analyzed edr.crowdstrike.falconstreaming.agents: 0 records
2025-07-03 16:10:38 - devo_backup - DEBUG - Successfully analyzed custom.logs.info: 0 records
2025-07-03 16:10:38 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:38 - devo_backup - DEBUG - Analyzing data volume for table: edr.crowdstrike.falconstreaming.other
2025-07-03 16:10:38 - devo_backup - DEBUG - Analyzing data volume for table: edr.crowdstrike.falconstreaming.user_activity_all
2025-07-03 16:10:38 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing count query for edr.crowdstrike.falconstreaming.other: from edr.crowdstrike.falconstreaming.other select count() as record_count
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing count query for edr.crowdstrike.falconstreaming.user_activity_all: from edr.crowdstrike.falconstreaming.user_activity_all select count() as record_count
2025-07-03 16:10:38 - devo_backup - DEBUG - Successfully analyzed cloud.office365.management.workplaceanalytics: 0 records
2025-07-03 16:10:38 - devo_backup - INFO - Executing Devo query: from edr.crowdstrike.falconstreaming.other select count() as record_count...
2025-07-03 16:10:38 - devo_backup - INFO - Executing Devo query: from edr.crowdstrike.falconstreaming.user_activity_all select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Analyzing data volume for table: edr.crowdstrike.falconstreaming.user_activity_detections
2025-07-03 16:10:38 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:38 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing count query for edr.crowdstrike.falconstreaming.user_activity_detections: from edr.crowdstrike.falconstreaming.user_activity_detections select count() as record_count
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing query (attempt 1): from edr.crowdstrike.falconstreaming.other select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing query (attempt 1): from edr.crowdstrike.falconstreaming.user_activity_all select count() as record_count...
2025-07-03 16:10:38 - devo_backup - INFO - Executing Devo query: from edr.crowdstrike.falconstreaming.user_activity_detections select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:38 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:38 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:38 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:38 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:38 - devo_backup - DEBUG - Executing query (attempt 1): from edr.crowdstrike.falconstreaming.user_activity_detections select count() as record_count...
2025-07-03 16:10:38 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:38 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:38 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:38 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:38 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:38 - devo_backup - DEBUG - Successfully analyzed edr.crowdstrike.falconstreaming.behaviors: 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Analyzing data volume for table: edr.crowdstrike.falconstreaming.user_activity_groups
2025-07-03 16:10:39 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing count query for edr.crowdstrike.falconstreaming.user_activity_groups: from edr.crowdstrike.falconstreaming.user_activity_groups select count() as record_count
2025-07-03 16:10:39 - devo_backup - DEBUG - Successfully analyzed edr.crowdstrike.falconstreaming.auth_activity: 0 records
2025-07-03 16:10:39 - devo_backup - INFO - Executing Devo query: from edr.crowdstrike.falconstreaming.user_activity_groups select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Analyzing data volume for table: edr.crowdstrike.falconstreaming.user_activity_prevention_policy
2025-07-03 16:10:39 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing count query for edr.crowdstrike.falconstreaming.user_activity_prevention_policy: from edr.crowdstrike.falconstreaming.user_activity_prevention_policy select count() as record_count
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing query (attempt 1): from edr.crowdstrike.falconstreaming.user_activity_groups select count() as record_count...
2025-07-03 16:10:39 - devo_backup - INFO - Executing Devo query: from edr.crowdstrike.falconstreaming.user_activity_prevention_policy select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:39 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:39 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing query (attempt 1): from edr.crowdstrike.falconstreaming.user_activity_prevention_policy select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:39 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:39 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:39 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:39 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:39 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Successfully analyzed edr.crowdstrike.falconstreaming.detection_summary: 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Successfully analyzed edr.crowdstrike.falconstreaming.alert: 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Analyzing data volume for table: firewall.all
2025-07-03 16:10:39 - devo_backup - DEBUG - Analyzing data volume for table: firewall.fortinet
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing count query for firewall.all: from firewall.all select count() as record_count
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing count query for firewall.fortinet: from firewall.fortinet select count() as record_count
2025-07-03 16:10:39 - devo_backup - INFO - Executing Devo query: from firewall.all select count() as record_count...
2025-07-03 16:10:39 - devo_backup - INFO - Executing Devo query: from firewall.fortinet select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:39 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing query (attempt 1): from firewall.all select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing query (attempt 1): from firewall.fortinet select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:39 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:39 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:39 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:39 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:39 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Successfully analyzed edr.crowdstrike.falconstreaming.epp_detection_summary: 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Analyzing data volume for table: firewall.fortinet.event.connector
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing count query for firewall.fortinet.event.connector: from firewall.fortinet.event.connector select count() as record_count
2025-07-03 16:10:39 - devo_backup - INFO - Executing Devo query: from firewall.fortinet.event.connector select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing query (attempt 1): from firewall.fortinet.event.connector select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:39 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:39 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:39 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Successfully analyzed edr.crowdstrike.falconstreaming.user_activity_all: 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Analyzing data volume for table: firewall.cisco.asa
2025-07-03 16:10:39 - devo_backup - INFO - Progress: 40/91 tables analyzed
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing count query for firewall.cisco.asa: from firewall.cisco.asa select count() as record_count
2025-07-03 16:10:39 - devo_backup - INFO - Executing Devo query: from firewall.cisco.asa select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing query (attempt 1): from firewall.cisco.asa select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:39 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:39 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:39 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:39 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Successfully analyzed edr.crowdstrike.falconstreaming.user_activity_detections: 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Successfully analyzed edr.crowdstrike.falconstreaming.other: 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Analyzing data volume for table: firewall.fortinet.event.ha
2025-07-03 16:10:39 - devo_backup - DEBUG - Analyzing data volume for table: firewall.fortinet.event.sdwan
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing count query for firewall.fortinet.event.ha: from firewall.fortinet.event.ha select count() as record_count
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing count query for firewall.fortinet.event.sdwan: from firewall.fortinet.event.sdwan select count() as record_count
2025-07-03 16:10:39 - devo_backup - INFO - Executing Devo query: from firewall.fortinet.event.ha select count() as record_count...
2025-07-03 16:10:39 - devo_backup - INFO - Executing Devo query: from firewall.fortinet.event.sdwan select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:39 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing query (attempt 1): from firewall.fortinet.event.ha select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing query (attempt 1): from firewall.fortinet.event.sdwan select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:39 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:39 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:39 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:39 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:39 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Successfully analyzed edr.crowdstrike.falconstreaming.user_activity_groups: 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Analyzing data volume for table: firewall.fortinet.event.system
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing count query for firewall.fortinet.event.system: from firewall.fortinet.event.system select count() as record_count
2025-07-03 16:10:39 - devo_backup - INFO - Executing Devo query: from firewall.fortinet.event.system select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing query (attempt 1): from firewall.fortinet.event.system select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:39 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:39 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:39 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Successfully analyzed edr.crowdstrike.falconstreaming.user_activity_prevention_policy: 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Analyzing data volume for table: firewall.fortinet.event.user
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing count query for firewall.fortinet.event.user: from firewall.fortinet.event.user select count() as record_count
2025-07-03 16:10:39 - devo_backup - INFO - Executing Devo query: from firewall.fortinet.event.user select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing query (attempt 1): from firewall.fortinet.event.user select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:39 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:39 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:39 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:39 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:39 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Successfully analyzed firewall.all: 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Successfully analyzed firewall.fortinet: 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Analyzing data volume for table: firewall.fortinet.event.vpn
2025-07-03 16:10:39 - devo_backup - DEBUG - Analyzing data volume for table: firewall.fortinet.traffic.forward
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing count query for firewall.fortinet.event.vpn: from firewall.fortinet.event.vpn select count() as record_count
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing count query for firewall.fortinet.traffic.forward: from firewall.fortinet.traffic.forward select count() as record_count
2025-07-03 16:10:39 - devo_backup - INFO - Executing Devo query: from firewall.fortinet.event.vpn select count() as record_count...
2025-07-03 16:10:39 - devo_backup - INFO - Executing Devo query: from firewall.fortinet.traffic.forward select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:39 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing query (attempt 1): from firewall.fortinet.event.vpn select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing query (attempt 1): from firewall.fortinet.traffic.forward select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:39 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:39 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:39 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:39 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:39 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Successfully analyzed firewall.fortinet.event.connector: 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Analyzing data volume for table: firewall.fortinet.traffic.local
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing count query for firewall.fortinet.traffic.local: from firewall.fortinet.traffic.local select count() as record_count
2025-07-03 16:10:39 - devo_backup - INFO - Executing Devo query: from firewall.fortinet.traffic.local select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing query (attempt 1): from firewall.fortinet.traffic.local select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:39 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:39 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:39 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Successfully analyzed firewall.fortinet.event.sdwan: 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Analyzing data volume for table: firewall.fortinet.utm.ssl
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing count query for firewall.fortinet.utm.ssl: from firewall.fortinet.utm.ssl select count() as record_count
2025-07-03 16:10:39 - devo_backup - INFO - Executing Devo query: from firewall.fortinet.utm.ssl select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing query (attempt 1): from firewall.fortinet.utm.ssl select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:39 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:39 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:39 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Successfully analyzed firewall.fortinet.event.system: 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Analyzing data volume for table: firewall.fortinet.utm.webfilter
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing count query for firewall.fortinet.utm.webfilter: from firewall.fortinet.utm.webfilter select count() as record_count
2025-07-03 16:10:39 - devo_backup - INFO - Executing Devo query: from firewall.fortinet.utm.webfilter select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing query (attempt 1): from firewall.fortinet.utm.webfilter select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:39 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:39 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:39 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:39 - devo_backup - DEBUG - Successfully analyzed firewall.cisco.asa: 0 records
2025-07-03 16:10:39 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Analyzing data volume for table: firewall.paloalto
2025-07-03 16:10:39 - devo_backup - INFO - Progress: 50/91 tables analyzed
2025-07-03 16:10:39 - devo_backup - DEBUG - Successfully analyzed firewall.fortinet.event.ha: 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing count query for firewall.paloalto: from firewall.paloalto select count() as record_count
2025-07-03 16:10:39 - devo_backup - DEBUG - Analyzing data volume for table: my.app.data
2025-07-03 16:10:39 - devo_backup - INFO - Executing Devo query: from firewall.paloalto select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing count query for my.app.data: from my.app.data select count() as record_count
2025-07-03 16:10:39 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:39 - devo_backup - INFO - Executing Devo query: from my.app.data select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing query (attempt 1): from firewall.paloalto select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:39 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing query (attempt 1): from my.app.data select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:39 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:39 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:39 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:39 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Successfully analyzed firewall.fortinet.event.user: 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Analyzing data volume for table: my.app.tngd.actiontraillinux
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing count query for my.app.tngd.actiontraillinux: from my.app.tngd.actiontraillinux select count() as record_count
2025-07-03 16:10:39 - devo_backup - INFO - Executing Devo query: from my.app.tngd.actiontraillinux select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing query (attempt 1): from my.app.tngd.actiontraillinux select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:39 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:39 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:39 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Successfully analyzed firewall.fortinet.traffic.forward: 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Analyzing data volume for table: my.app.tngd.actiontrailwindows
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing count query for my.app.tngd.actiontrailwindows: from my.app.tngd.actiontrailwindows select count() as record_count
2025-07-03 16:10:39 - devo_backup - INFO - Executing Devo query: from my.app.tngd.actiontrailwindows select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing query (attempt 1): from my.app.tngd.actiontrailwindows select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:39 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:39 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:39 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Successfully analyzed firewall.fortinet.event.vpn: 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Analyzing data volume for table: my.app.tngd.adminportal
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing count query for my.app.tngd.adminportal: from my.app.tngd.adminportal select count() as record_count
2025-07-03 16:10:39 - devo_backup - INFO - Executing Devo query: from my.app.tngd.adminportal select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing query (attempt 1): from my.app.tngd.adminportal select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:39 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:39 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:39 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Successfully analyzed firewall.fortinet.traffic.local: 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Analyzing data volume for table: my.app.tngd.cfw
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing count query for my.app.tngd.cfw: from my.app.tngd.cfw select count() as record_count
2025-07-03 16:10:39 - devo_backup - INFO - Executing Devo query: from my.app.tngd.cfw select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing query (attempt 1): from my.app.tngd.cfw select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:39 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:39 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:39 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Successfully analyzed firewall.fortinet.utm.webfilter: 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Analyzing data volume for table: my.app.tngd.ciscoswitch
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing count query for my.app.tngd.ciscoswitch: from my.app.tngd.ciscoswitch select count() as record_count
2025-07-03 16:10:39 - devo_backup - INFO - Executing Devo query: from my.app.tngd.ciscoswitch select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing query (attempt 1): from my.app.tngd.ciscoswitch select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:39 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:39 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:39 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Successfully analyzed firewall.fortinet.utm.ssl: 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Analyzing data volume for table: my.app.tngd.cyberark
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing count query for my.app.tngd.cyberark: from my.app.tngd.cyberark select count() as record_count
2025-07-03 16:10:39 - devo_backup - INFO - Executing Devo query: from my.app.tngd.cyberark select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing query (attempt 1): from my.app.tngd.cyberark select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:39 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:39 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:39 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:39 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:39 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:39 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:39 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Successfully analyzed my.app.tngd.actiontraillinux: 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Successfully analyzed my.app.data: 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Successfully analyzed firewall.paloalto: 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Analyzing data volume for table: my.app.tngd.ezeelogin
2025-07-03 16:10:39 - devo_backup - DEBUG - Analyzing data volume for table: my.app.tngd.h3ccoreswitch
2025-07-03 16:10:39 - devo_backup - DEBUG - Analyzing data volume for table: my.app.tngd.h3cswitch
2025-07-03 16:10:39 - devo_backup - INFO - Progress: 60/91 tables analyzed
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing count query for my.app.tngd.ezeelogin: from my.app.tngd.ezeelogin select count() as record_count
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing count query for my.app.tngd.h3ccoreswitch: from my.app.tngd.h3ccoreswitch select count() as record_count
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing count query for my.app.tngd.h3cswitch: from my.app.tngd.h3cswitch select count() as record_count
2025-07-03 16:10:39 - devo_backup - INFO - Executing Devo query: from my.app.tngd.ezeelogin select count() as record_count...
2025-07-03 16:10:39 - devo_backup - INFO - Executing Devo query: from my.app.tngd.h3ccoreswitch select count() as record_count...
2025-07-03 16:10:39 - devo_backup - INFO - Executing Devo query: from my.app.tngd.h3cswitch select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:39 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:39 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing query (attempt 1): from my.app.tngd.ezeelogin select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing query (attempt 1): from my.app.tngd.h3ccoreswitch select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing query (attempt 1): from my.app.tngd.h3cswitch select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:39 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:39 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:39 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:39 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:39 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:39 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:39 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Successfully analyzed my.app.tngd.actiontrailwindows: 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Analyzing data volume for table: my.app.tngd.h3cwirelessctrl
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing count query for my.app.tngd.h3cwirelessctrl: from my.app.tngd.h3cwirelessctrl select count() as record_count
2025-07-03 16:10:39 - devo_backup - INFO - Executing Devo query: from my.app.tngd.h3cwirelessctrl select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing query (attempt 1): from my.app.tngd.h3cwirelessctrl select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:39 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:39 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:39 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Successfully analyzed my.app.tngd.adminportal: 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Analyzing data volume for table: my.app.tngd.keeper
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing count query for my.app.tngd.keeper: from my.app.tngd.keeper select count() as record_count
2025-07-03 16:10:39 - devo_backup - INFO - Executing Devo query: from my.app.tngd.keeper select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing query (attempt 1): from my.app.tngd.keeper select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:39 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:39 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:39 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Successfully analyzed my.app.tngd.cfw: 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Analyzing data volume for table: my.app.tngd.polardb
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing count query for my.app.tngd.polardb: from my.app.tngd.polardb select count() as record_count
2025-07-03 16:10:39 - devo_backup - INFO - Executing Devo query: from my.app.tngd.polardb select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing query (attempt 1): from my.app.tngd.polardb select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:39 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:39 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:39 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:39 - devo_backup - DEBUG - Successfully analyzed my.app.tngd.ciscoswitch: 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Analyzing data volume for table: my.app.tngd.rds
2025-07-03 16:10:39 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing count query for my.app.tngd.rds: from my.app.tngd.rds select count() as record_count
2025-07-03 16:10:39 - devo_backup - DEBUG - Successfully analyzed my.app.tngd.cyberark: 0 records
2025-07-03 16:10:39 - devo_backup - INFO - Executing Devo query: from my.app.tngd.rds select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Analyzing data volume for table: my.app.tngd.sas
2025-07-03 16:10:39 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing count query for my.app.tngd.sas: from my.app.tngd.sas select count() as record_count
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing query (attempt 1): from my.app.tngd.rds select count() as record_count...
2025-07-03 16:10:39 - devo_backup - INFO - Executing Devo query: from my.app.tngd.sas select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:39 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:39 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing query (attempt 1): from my.app.tngd.sas select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:39 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:39 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:39 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Successfully analyzed my.app.tngd.ezeelogin: 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Analyzing data volume for table: my.app.tngd.waf
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing count query for my.app.tngd.waf: from my.app.tngd.waf select count() as record_count
2025-07-03 16:10:39 - devo_backup - INFO - Executing Devo query: from my.app.tngd.waf select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing query (attempt 1): from my.app.tngd.waf select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:39 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:39 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:39 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Successfully analyzed my.app.tngd.h3ccoreswitch: 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Analyzing data volume for table: netstat.zscaler.analyzer_zpa
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing count query for netstat.zscaler.analyzer_zpa: from netstat.zscaler.analyzer_zpa select count() as record_count
2025-07-03 16:10:39 - devo_backup - INFO - Executing Devo query: from netstat.zscaler.analyzer_zpa select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing query (attempt 1): from netstat.zscaler.analyzer_zpa select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:39 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:39 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:39 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Successfully analyzed my.app.tngd.h3cswitch: 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Analyzing data volume for table: nac.aruba.clearpass.session
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing count query for nac.aruba.clearpass.session: from nac.aruba.clearpass.session select count() as record_count
2025-07-03 16:10:39 - devo_backup - INFO - Executing Devo query: from nac.aruba.clearpass.session select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing query (attempt 1): from nac.aruba.clearpass.session select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:39 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:39 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:39 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Successfully analyzed my.app.tngd.h3cwirelessctrl: 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Analyzing data volume for table: network.switch.info
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing count query for network.switch.info: from network.switch.info select count() as record_count
2025-07-03 16:10:39 - devo_backup - INFO - Executing Devo query: from network.switch.info select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:39 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing query (attempt 1): from network.switch.info select count() as record_count...
2025-07-03 16:10:39 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:39 - devo_backup - DEBUG - Successfully analyzed my.app.tngd.keeper: 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:39 - devo_backup - DEBUG - Analyzing data volume for table: network.cisco.router
2025-07-03 16:10:39 - devo_backup - INFO - Progress: 70/91 tables analyzed
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing count query for network.cisco.router: from network.cisco.router select count() as record_count
2025-07-03 16:10:39 - devo_backup - INFO - Executing Devo query: from network.cisco.router select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing query (attempt 1): from network.cisco.router select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:39 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:39 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:39 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Successfully analyzed my.app.tngd.polardb: 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Analyzing data volume for table: siem.logtrust.alert.info
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing count query for siem.logtrust.alert.info: from siem.logtrust.alert.info select count() as record_count
2025-07-03 16:10:39 - devo_backup - INFO - Executing Devo query: from siem.logtrust.alert.info select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.alert.info select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:39 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:39 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:39 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Successfully analyzed my.app.tngd.rds: 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Analyzing data volume for table: siem.logtrust.auth.info
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing count query for siem.logtrust.auth.info: from siem.logtrust.auth.info select count() as record_count
2025-07-03 16:10:39 - devo_backup - INFO - Executing Devo query: from siem.logtrust.auth.info select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.auth.info select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:39 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:39 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:39 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:39 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:39 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Successfully analyzed my.app.tngd.sas: 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Successfully analyzed my.app.tngd.waf: 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Analyzing data volume for table: siem.logtrust.dns.info
2025-07-03 16:10:39 - devo_backup - DEBUG - Analyzing data volume for table: siem.logtrust.network.info
2025-07-03 16:10:39 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing count query for siem.logtrust.dns.info: from siem.logtrust.dns.info select count() as record_count
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing count query for siem.logtrust.network.info: from siem.logtrust.network.info select count() as record_count
2025-07-03 16:10:39 - devo_backup - INFO - Executing Devo query: from siem.logtrust.dns.info select count() as record_count...
2025-07-03 16:10:39 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:39 - devo_backup - INFO - Executing Devo query: from siem.logtrust.network.info select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:39 - devo_backup - DEBUG - Successfully analyzed netstat.zscaler.analyzer_zpa: 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.network.info select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Analyzing data volume for table: siem.logtrust.process.info
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.dns.info select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing count query for siem.logtrust.process.info: from siem.logtrust.process.info select count() as record_count
2025-07-03 16:10:39 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:39 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:39 - devo_backup - INFO - Executing Devo query: from siem.logtrust.process.info select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:39 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:39 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.process.info select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:39 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:39 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Successfully analyzed nac.aruba.clearpass.session: 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:39 - devo_backup - DEBUG - Successfully analyzed network.switch.info: 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Analyzing data volume for table: siem.logtrust.file.info
2025-07-03 16:10:39 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:39 - devo_backup - DEBUG - Analyzing data volume for table: siem.logtrust.registry.info
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing count query for siem.logtrust.file.info: from siem.logtrust.file.info select count() as record_count
2025-07-03 16:10:39 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:39 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing count query for siem.logtrust.registry.info: from siem.logtrust.registry.info select count() as record_count
2025-07-03 16:10:39 - devo_backup - INFO - Executing Devo query: from siem.logtrust.file.info select count() as record_count...
2025-07-03 16:10:39 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Successfully analyzed network.cisco.router: 0 records
2025-07-03 16:10:39 - devo_backup - INFO - Executing Devo query: from siem.logtrust.registry.info select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:39 - devo_backup - DEBUG - Successfully analyzed siem.logtrust.alert.info: 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Analyzing data volume for table: siem.logtrust.system.info
2025-07-03 16:10:39 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.file.info select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Analyzing data volume for table: siem.logtrust.web.activity
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing count query for siem.logtrust.system.info: from siem.logtrust.system.info select count() as record_count
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.registry.info select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing count query for siem.logtrust.web.activity: from siem.logtrust.web.activity select count() as record_count
2025-07-03 16:10:39 - devo_backup - INFO - Executing Devo query: from siem.logtrust.system.info select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:39 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:39 - devo_backup - INFO - Executing Devo query: from siem.logtrust.web.activity select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:39 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:39 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.system.info select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing query (attempt 1): from siem.logtrust.web.activity select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:39 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:39 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:39 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:39 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:39 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Successfully analyzed siem.logtrust.auth.info: 0 records
2025-07-03 16:10:39 - devo_backup - DEBUG - Analyzing data volume for table: system.linux.syslog
2025-07-03 16:10:39 - devo_backup - INFO - Progress: 80/91 tables analyzed
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing count query for system.linux.syslog: from system.linux.syslog select count() as record_count
2025-07-03 16:10:39 - devo_backup - INFO - Executing Devo query: from system.linux.syslog select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:39 - devo_backup - DEBUG - Executing query (attempt 1): from system.linux.syslog select count() as record_count...
2025-07-03 16:10:39 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:39 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:40 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:40 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:40 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:40 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:40 - devo_backup - DEBUG - Successfully analyzed siem.logtrust.dns.info: 0 records
2025-07-03 16:10:40 - devo_backup - DEBUG - Successfully analyzed siem.logtrust.network.info: 0 records
2025-07-03 16:10:40 - devo_backup - DEBUG - Analyzing data volume for table: system.auth.login
2025-07-03 16:10:40 - devo_backup - DEBUG - Analyzing data volume for table: system.windows.eventlog
2025-07-03 16:10:40 - devo_backup - DEBUG - Executing count query for system.auth.login: from system.auth.login select count() as record_count
2025-07-03 16:10:40 - devo_backup - DEBUG - Executing count query for system.windows.eventlog: from system.windows.eventlog select count() as record_count
2025-07-03 16:10:40 - devo_backup - INFO - Executing Devo query: from system.auth.login select count() as record_count...
2025-07-03 16:10:40 - devo_backup - INFO - Executing Devo query: from system.windows.eventlog select count() as record_count...
2025-07-03 16:10:40 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:40 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:40 - devo_backup - DEBUG - Executing query (attempt 1): from system.auth.login select count() as record_count...
2025-07-03 16:10:40 - devo_backup - DEBUG - Executing query (attempt 1): from system.windows.eventlog select count() as record_count...
2025-07-03 16:10:40 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:40 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:40 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:40 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:40 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:40 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:40 - devo_backup - DEBUG - Successfully analyzed siem.logtrust.web.activity: 0 records
2025-07-03 16:10:40 - devo_backup - DEBUG - Analyzing data volume for table: user.activity.log
2025-07-03 16:10:40 - devo_backup - DEBUG - Executing count query for user.activity.log: from user.activity.log select count() as record_count
2025-07-03 16:10:40 - devo_backup - INFO - Executing Devo query: from user.activity.log select count() as record_count...
2025-07-03 16:10:40 - devo_backup - DEBUG - Query time range: 2025-07-01T00:00:00 to 2025-07-01T23:59:59.999999
2025-07-03 16:10:40 - devo_backup - DEBUG - Executing query (attempt 1): from user.activity.log select count() as record_count...
2025-07-03 16:10:40 - devo_backup - DEBUG - Query executed successfully on attempt 1
2025-07-03 16:10:40 - devo_backup - DEBUG - Query result type: <class 'generator'>
2025-07-03 16:10:40 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:40 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:40 - devo_backup - DEBUG - Successfully analyzed siem.logtrust.file.info: 0 records
2025-07-03 16:10:40 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:40 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:40 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:40 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:40 - devo_backup - DEBUG - Successfully analyzed siem.logtrust.process.info: 0 records
2025-07-03 16:10:40 - devo_backup - DEBUG - Successfully analyzed siem.logtrust.system.info: 0 records
2025-07-03 16:10:40 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:40 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:40 - devo_backup - DEBUG - Successfully analyzed siem.logtrust.registry.info: 0 records
2025-07-03 16:10:40 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:40 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:40 - devo_backup - DEBUG - Successfully analyzed system.linux.syslog: 0 records
2025-07-03 16:10:40 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:40 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:40 - devo_backup - DEBUG - Successfully analyzed system.windows.eventlog: 0 records
2025-07-03 16:10:40 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:40 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:40 - devo_backup - DEBUG - Successfully analyzed system.auth.login: 0 records
2025-07-03 16:10:40 - devo_backup - INFO - Progress: 90/91 tables analyzed
2025-07-03 16:10:40 - devo_backup - DEBUG - Error consuming generator: Bad parameters
2025-07-03 16:10:40 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:10:40 - devo_backup - DEBUG - Successfully analyzed user.activity.log: 0 records
2025-07-03 16:10:40 - devo_backup - INFO - Progress: 91/91 tables analyzed
2025-07-03 16:10:40 - devo_backup - INFO - Analysis results saved to: results\data_volume_analysis_20250701_20250703_161040.json
2025-07-03 16:10:40 - devo_backup - INFO - Data volume analysis completed successfully!
