2025-07-03 16:12:03 - devo_backup - INFO - Logging initialized. Log file: logs/devo_backup_20250703_161203.log
2025-07-03 16:12:03 - devo_backup - INFO - Testing Devo tables across multiple dates to find actual data...
2025-07-03 16:12:03 - devo_backup - INFO - Testing dates: 2025-07-03, 2025-07-02, 2025-07-01, 2025-06-30, 2025-06-29, 2025-06-28, 2025-06-27, 2025-06-26, 2025-06-25, 2025-06-24, 2025-06-23, 2025-06-22, 2025-06-21, 2025-06-20
2025-07-03 16:12:03 - devo_backup - INFO - Devo SDK client initialized with endpoint: https://api-apac.devo.com/search/query
2025-07-03 16:12:03 - devo_backup - INFO - Testing table 'my.app.tngd.actiontraillinux' across 14 dates...
2025-07-03 16:12:03 - devo_backup - INFO - Testing my.app.tngd.actiontraillinux for date 2025-07-03...
2025-07-03 16:12:03 - devo_backup - INFO - Executing Devo query: from my.app.tngd.actiontraillinux select count() as record_count...
2025-07-03 16:12:03 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:12:03 - devo_backup - INFO -   2025-07-03: 0 records
2025-07-03 16:12:03 - devo_backup - INFO - Testing my.app.tngd.actiontraillinux for date 2025-07-02...
2025-07-03 16:12:03 - devo_backup - INFO - Executing Devo query: from my.app.tngd.actiontraillinux select count() as record_count...
2025-07-03 16:12:03 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:12:03 - devo_backup - INFO -   2025-07-02: 0 records
2025-07-03 16:12:03 - devo_backup - INFO - Testing my.app.tngd.actiontraillinux for date 2025-07-01...
2025-07-03 16:12:03 - devo_backup - INFO - Executing Devo query: from my.app.tngd.actiontraillinux select count() as record_count...
2025-07-03 16:12:03 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:12:03 - devo_backup - INFO -   2025-07-01: 0 records
2025-07-03 16:12:03 - devo_backup - INFO - Testing my.app.tngd.actiontraillinux for date 2025-06-30...
2025-07-03 16:12:03 - devo_backup - INFO - Executing Devo query: from my.app.tngd.actiontraillinux select count() as record_count...
2025-07-03 16:12:03 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:12:03 - devo_backup - INFO -   2025-06-30: 0 records
2025-07-03 16:12:03 - devo_backup - INFO - Testing my.app.tngd.actiontraillinux for date 2025-06-29...
2025-07-03 16:12:03 - devo_backup - INFO - Executing Devo query: from my.app.tngd.actiontraillinux select count() as record_count...
2025-07-03 16:12:03 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:12:03 - devo_backup - INFO -   2025-06-29: 0 records
2025-07-03 16:12:03 - devo_backup - INFO - Testing my.app.tngd.actiontraillinux for date 2025-06-28...
2025-07-03 16:12:03 - devo_backup - INFO - Executing Devo query: from my.app.tngd.actiontraillinux select count() as record_count...
2025-07-03 16:12:03 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:12:03 - devo_backup - INFO -   2025-06-28: 0 records
2025-07-03 16:12:03 - devo_backup - INFO - Testing my.app.tngd.actiontraillinux for date 2025-06-27...
2025-07-03 16:12:03 - devo_backup - INFO - Executing Devo query: from my.app.tngd.actiontraillinux select count() as record_count...
2025-07-03 16:12:04 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:12:04 - devo_backup - INFO -   2025-06-27: 0 records
2025-07-03 16:12:04 - devo_backup - INFO - Testing my.app.tngd.actiontraillinux for date 2025-06-26...
2025-07-03 16:12:04 - devo_backup - INFO - Executing Devo query: from my.app.tngd.actiontraillinux select count() as record_count...
2025-07-03 16:12:04 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:12:04 - devo_backup - INFO -   2025-06-26: 0 records
2025-07-03 16:12:04 - devo_backup - INFO - Testing my.app.tngd.actiontraillinux for date 2025-06-25...
2025-07-03 16:12:04 - devo_backup - INFO - Executing Devo query: from my.app.tngd.actiontraillinux select count() as record_count...
2025-07-03 16:12:04 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:12:04 - devo_backup - INFO -   2025-06-25: 0 records
2025-07-03 16:12:04 - devo_backup - INFO - Testing my.app.tngd.actiontraillinux for date 2025-06-24...
2025-07-03 16:12:04 - devo_backup - INFO - Executing Devo query: from my.app.tngd.actiontraillinux select count() as record_count...
2025-07-03 16:12:04 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:12:04 - devo_backup - INFO -   2025-06-24: 0 records
2025-07-03 16:12:04 - devo_backup - INFO - Testing my.app.tngd.actiontraillinux for date 2025-06-23...
2025-07-03 16:12:04 - devo_backup - INFO - Executing Devo query: from my.app.tngd.actiontraillinux select count() as record_count...
2025-07-03 16:12:04 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:12:04 - devo_backup - INFO -   2025-06-23: 0 records
2025-07-03 16:12:04 - devo_backup - INFO - Testing my.app.tngd.actiontraillinux for date 2025-06-22...
2025-07-03 16:12:04 - devo_backup - INFO - Executing Devo query: from my.app.tngd.actiontraillinux select count() as record_count...
2025-07-03 16:12:04 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:12:04 - devo_backup - INFO -   2025-06-22: 0 records
2025-07-03 16:12:04 - devo_backup - INFO - Testing my.app.tngd.actiontraillinux for date 2025-06-21...
2025-07-03 16:12:04 - devo_backup - INFO - Executing Devo query: from my.app.tngd.actiontraillinux select count() as record_count...
2025-07-03 16:12:04 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:12:04 - devo_backup - INFO -   2025-06-21: 0 records
2025-07-03 16:12:04 - devo_backup - INFO - Testing my.app.tngd.actiontraillinux for date 2025-06-20...
2025-07-03 16:12:04 - devo_backup - INFO - Executing Devo query: from my.app.tngd.actiontraillinux select count() as record_count...
2025-07-03 16:12:04 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:12:04 - devo_backup - INFO -   2025-06-20: 0 records
2025-07-03 16:12:04 - devo_backup - INFO - Devo SDK client initialized with endpoint: https://api-apac.devo.com/search/query
2025-07-03 16:12:04 - devo_backup - INFO - Testing table 'my.app.tngd.waf' across 14 dates...
2025-07-03 16:12:04 - devo_backup - INFO - Testing my.app.tngd.waf for date 2025-07-03...
2025-07-03 16:12:04 - devo_backup - INFO - Executing Devo query: from my.app.tngd.waf select count() as record_count...
2025-07-03 16:12:05 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:12:05 - devo_backup - INFO -   2025-07-03: 0 records
2025-07-03 16:12:05 - devo_backup - INFO - Testing my.app.tngd.waf for date 2025-07-02...
2025-07-03 16:12:05 - devo_backup - INFO - Executing Devo query: from my.app.tngd.waf select count() as record_count...
2025-07-03 16:12:05 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:12:05 - devo_backup - INFO -   2025-07-02: 0 records
2025-07-03 16:12:05 - devo_backup - INFO - Testing my.app.tngd.waf for date 2025-07-01...
2025-07-03 16:12:05 - devo_backup - INFO - Executing Devo query: from my.app.tngd.waf select count() as record_count...
2025-07-03 16:12:05 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:12:05 - devo_backup - INFO -   2025-07-01: 0 records
2025-07-03 16:12:05 - devo_backup - INFO - Testing my.app.tngd.waf for date 2025-06-30...
2025-07-03 16:12:05 - devo_backup - INFO - Executing Devo query: from my.app.tngd.waf select count() as record_count...
2025-07-03 16:12:05 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:12:05 - devo_backup - INFO -   2025-06-30: 0 records
2025-07-03 16:12:05 - devo_backup - INFO - Testing my.app.tngd.waf for date 2025-06-29...
2025-07-03 16:12:05 - devo_backup - INFO - Executing Devo query: from my.app.tngd.waf select count() as record_count...
2025-07-03 16:12:05 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:12:05 - devo_backup - INFO -   2025-06-29: 0 records
2025-07-03 16:12:05 - devo_backup - INFO - Testing my.app.tngd.waf for date 2025-06-28...
2025-07-03 16:12:05 - devo_backup - INFO - Executing Devo query: from my.app.tngd.waf select count() as record_count...
2025-07-03 16:12:05 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:12:05 - devo_backup - INFO -   2025-06-28: 0 records
2025-07-03 16:12:05 - devo_backup - INFO - Testing my.app.tngd.waf for date 2025-06-27...
2025-07-03 16:12:05 - devo_backup - INFO - Executing Devo query: from my.app.tngd.waf select count() as record_count...
2025-07-03 16:12:05 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:12:05 - devo_backup - INFO -   2025-06-27: 0 records
2025-07-03 16:12:05 - devo_backup - INFO - Testing my.app.tngd.waf for date 2025-06-26...
2025-07-03 16:12:05 - devo_backup - INFO - Executing Devo query: from my.app.tngd.waf select count() as record_count...
2025-07-03 16:12:06 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:12:06 - devo_backup - INFO -   2025-06-26: 0 records
2025-07-03 16:12:06 - devo_backup - INFO - Testing my.app.tngd.waf for date 2025-06-25...
2025-07-03 16:12:06 - devo_backup - INFO - Executing Devo query: from my.app.tngd.waf select count() as record_count...
2025-07-03 16:12:06 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:12:06 - devo_backup - INFO -   2025-06-25: 0 records
2025-07-03 16:12:06 - devo_backup - INFO - Testing my.app.tngd.waf for date 2025-06-24...
2025-07-03 16:12:06 - devo_backup - INFO - Executing Devo query: from my.app.tngd.waf select count() as record_count...
2025-07-03 16:12:06 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:12:06 - devo_backup - INFO -   2025-06-24: 0 records
2025-07-03 16:12:06 - devo_backup - INFO - Testing my.app.tngd.waf for date 2025-06-23...
2025-07-03 16:12:06 - devo_backup - INFO - Executing Devo query: from my.app.tngd.waf select count() as record_count...
2025-07-03 16:12:06 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:12:06 - devo_backup - INFO -   2025-06-23: 0 records
2025-07-03 16:12:06 - devo_backup - INFO - Testing my.app.tngd.waf for date 2025-06-22...
2025-07-03 16:12:06 - devo_backup - INFO - Executing Devo query: from my.app.tngd.waf select count() as record_count...
2025-07-03 16:12:06 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:12:06 - devo_backup - INFO -   2025-06-22: 0 records
2025-07-03 16:12:06 - devo_backup - INFO - Testing my.app.tngd.waf for date 2025-06-21...
2025-07-03 16:12:06 - devo_backup - INFO - Executing Devo query: from my.app.tngd.waf select count() as record_count...
2025-07-03 16:12:06 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:12:06 - devo_backup - INFO -   2025-06-21: 0 records
2025-07-03 16:12:06 - devo_backup - INFO - Testing my.app.tngd.waf for date 2025-06-20...
2025-07-03 16:12:06 - devo_backup - INFO - Executing Devo query: from my.app.tngd.waf select count() as record_count...
2025-07-03 16:12:06 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:12:06 - devo_backup - INFO -   2025-06-20: 0 records
2025-07-03 16:12:06 - devo_backup - INFO - Devo SDK client initialized with endpoint: https://api-apac.devo.com/search/query
2025-07-03 16:12:06 - devo_backup - INFO - Testing table 'cloud.office365.management.exchange' across 14 dates...
2025-07-03 16:12:06 - devo_backup - INFO - Testing cloud.office365.management.exchange for date 2025-07-03...
2025-07-03 16:12:06 - devo_backup - INFO - Executing Devo query: from cloud.office365.management.exchange select count() as record_count...
2025-07-03 16:12:06 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:12:06 - devo_backup - INFO -   2025-07-03: 0 records
2025-07-03 16:12:06 - devo_backup - INFO - Testing cloud.office365.management.exchange for date 2025-07-02...
2025-07-03 16:12:06 - devo_backup - INFO - Executing Devo query: from cloud.office365.management.exchange select count() as record_count...
2025-07-03 16:12:07 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:12:07 - devo_backup - INFO -   2025-07-02: 0 records
2025-07-03 16:12:07 - devo_backup - INFO - Testing cloud.office365.management.exchange for date 2025-07-01...
2025-07-03 16:12:07 - devo_backup - INFO - Executing Devo query: from cloud.office365.management.exchange select count() as record_count...
2025-07-03 16:12:07 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:12:07 - devo_backup - INFO -   2025-07-01: 0 records
2025-07-03 16:12:07 - devo_backup - INFO - Testing cloud.office365.management.exchange for date 2025-06-30...
2025-07-03 16:12:07 - devo_backup - INFO - Executing Devo query: from cloud.office365.management.exchange select count() as record_count...
2025-07-03 16:12:07 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:12:07 - devo_backup - INFO -   2025-06-30: 0 records
2025-07-03 16:12:07 - devo_backup - INFO - Testing cloud.office365.management.exchange for date 2025-06-29...
2025-07-03 16:12:07 - devo_backup - INFO - Executing Devo query: from cloud.office365.management.exchange select count() as record_count...
2025-07-03 16:12:07 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:12:07 - devo_backup - INFO -   2025-06-29: 0 records
2025-07-03 16:12:07 - devo_backup - INFO - Testing cloud.office365.management.exchange for date 2025-06-28...
2025-07-03 16:12:07 - devo_backup - INFO - Executing Devo query: from cloud.office365.management.exchange select count() as record_count...
2025-07-03 16:12:07 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:12:07 - devo_backup - INFO -   2025-06-28: 0 records
2025-07-03 16:12:07 - devo_backup - INFO - Testing cloud.office365.management.exchange for date 2025-06-27...
2025-07-03 16:12:07 - devo_backup - INFO - Executing Devo query: from cloud.office365.management.exchange select count() as record_count...
2025-07-03 16:12:07 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:12:07 - devo_backup - INFO -   2025-06-27: 0 records
2025-07-03 16:12:07 - devo_backup - INFO - Testing cloud.office365.management.exchange for date 2025-06-26...
2025-07-03 16:12:07 - devo_backup - INFO - Executing Devo query: from cloud.office365.management.exchange select count() as record_count...
2025-07-03 16:12:07 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:12:07 - devo_backup - INFO -   2025-06-26: 0 records
2025-07-03 16:12:07 - devo_backup - INFO - Testing cloud.office365.management.exchange for date 2025-06-25...
2025-07-03 16:12:07 - devo_backup - INFO - Executing Devo query: from cloud.office365.management.exchange select count() as record_count...
2025-07-03 16:12:08 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:12:08 - devo_backup - INFO -   2025-06-25: 0 records
2025-07-03 16:12:08 - devo_backup - INFO - Testing cloud.office365.management.exchange for date 2025-06-24...
2025-07-03 16:12:08 - devo_backup - INFO - Executing Devo query: from cloud.office365.management.exchange select count() as record_count...
2025-07-03 16:12:08 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:12:08 - devo_backup - INFO -   2025-06-24: 0 records
2025-07-03 16:12:08 - devo_backup - INFO - Testing cloud.office365.management.exchange for date 2025-06-23...
2025-07-03 16:12:08 - devo_backup - INFO - Executing Devo query: from cloud.office365.management.exchange select count() as record_count...
2025-07-03 16:12:08 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:12:08 - devo_backup - INFO -   2025-06-23: 0 records
2025-07-03 16:12:08 - devo_backup - INFO - Testing cloud.office365.management.exchange for date 2025-06-22...
2025-07-03 16:12:08 - devo_backup - INFO - Executing Devo query: from cloud.office365.management.exchange select count() as record_count...
2025-07-03 16:12:08 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:12:08 - devo_backup - INFO -   2025-06-22: 0 records
2025-07-03 16:12:08 - devo_backup - INFO - Testing cloud.office365.management.exchange for date 2025-06-21...
2025-07-03 16:12:08 - devo_backup - INFO - Executing Devo query: from cloud.office365.management.exchange select count() as record_count...
2025-07-03 16:12:08 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:12:08 - devo_backup - INFO -   2025-06-21: 0 records
2025-07-03 16:12:08 - devo_backup - INFO - Testing cloud.office365.management.exchange for date 2025-06-20...
2025-07-03 16:12:08 - devo_backup - INFO - Executing Devo query: from cloud.office365.management.exchange select count() as record_count...
2025-07-03 16:12:08 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:12:08 - devo_backup - INFO -   2025-06-20: 0 records
2025-07-03 16:12:08 - devo_backup - INFO - Devo SDK client initialized with endpoint: https://api-apac.devo.com/search/query
2025-07-03 16:12:08 - devo_backup - INFO - Testing table 'edr.crowdstrike.falconstreaming.agents' across 14 dates...
2025-07-03 16:12:08 - devo_backup - INFO - Testing edr.crowdstrike.falconstreaming.agents for date 2025-07-03...
2025-07-03 16:12:08 - devo_backup - INFO - Executing Devo query: from edr.crowdstrike.falconstreaming.agents select count() as record_count...
2025-07-03 16:12:08 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:12:08 - devo_backup - INFO -   2025-07-03: 0 records
2025-07-03 16:12:08 - devo_backup - INFO - Testing edr.crowdstrike.falconstreaming.agents for date 2025-07-02...
2025-07-03 16:12:08 - devo_backup - INFO - Executing Devo query: from edr.crowdstrike.falconstreaming.agents select count() as record_count...
2025-07-03 16:12:09 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:12:09 - devo_backup - INFO -   2025-07-02: 0 records
2025-07-03 16:12:09 - devo_backup - INFO - Testing edr.crowdstrike.falconstreaming.agents for date 2025-07-01...
2025-07-03 16:12:09 - devo_backup - INFO - Executing Devo query: from edr.crowdstrike.falconstreaming.agents select count() as record_count...
2025-07-03 16:12:09 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:12:09 - devo_backup - INFO -   2025-07-01: 0 records
2025-07-03 16:12:09 - devo_backup - INFO - Testing edr.crowdstrike.falconstreaming.agents for date 2025-06-30...
2025-07-03 16:12:09 - devo_backup - INFO - Executing Devo query: from edr.crowdstrike.falconstreaming.agents select count() as record_count...
2025-07-03 16:12:09 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:12:09 - devo_backup - INFO -   2025-06-30: 0 records
2025-07-03 16:12:09 - devo_backup - INFO - Testing edr.crowdstrike.falconstreaming.agents for date 2025-06-29...
2025-07-03 16:12:09 - devo_backup - INFO - Executing Devo query: from edr.crowdstrike.falconstreaming.agents select count() as record_count...
2025-07-03 16:12:09 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:12:09 - devo_backup - INFO -   2025-06-29: 0 records
2025-07-03 16:12:09 - devo_backup - INFO - Testing edr.crowdstrike.falconstreaming.agents for date 2025-06-28...
2025-07-03 16:12:09 - devo_backup - INFO - Executing Devo query: from edr.crowdstrike.falconstreaming.agents select count() as record_count...
2025-07-03 16:12:09 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:12:09 - devo_backup - INFO -   2025-06-28: 0 records
2025-07-03 16:12:09 - devo_backup - INFO - Testing edr.crowdstrike.falconstreaming.agents for date 2025-06-27...
2025-07-03 16:12:09 - devo_backup - INFO - Executing Devo query: from edr.crowdstrike.falconstreaming.agents select count() as record_count...
2025-07-03 16:12:09 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:12:09 - devo_backup - INFO -   2025-06-27: 0 records
2025-07-03 16:12:09 - devo_backup - INFO - Testing edr.crowdstrike.falconstreaming.agents for date 2025-06-26...
2025-07-03 16:12:09 - devo_backup - INFO - Executing Devo query: from edr.crowdstrike.falconstreaming.agents select count() as record_count...
2025-07-03 16:12:09 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:12:09 - devo_backup - INFO -   2025-06-26: 0 records
2025-07-03 16:12:09 - devo_backup - INFO - Testing edr.crowdstrike.falconstreaming.agents for date 2025-06-25...
2025-07-03 16:12:09 - devo_backup - INFO - Executing Devo query: from edr.crowdstrike.falconstreaming.agents select count() as record_count...
2025-07-03 16:12:09 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:12:09 - devo_backup - INFO -   2025-06-25: 0 records
2025-07-03 16:12:09 - devo_backup - INFO - Testing edr.crowdstrike.falconstreaming.agents for date 2025-06-24...
2025-07-03 16:12:09 - devo_backup - INFO - Executing Devo query: from edr.crowdstrike.falconstreaming.agents select count() as record_count...
2025-07-03 16:12:10 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:12:10 - devo_backup - INFO -   2025-06-24: 0 records
2025-07-03 16:12:10 - devo_backup - INFO - Testing edr.crowdstrike.falconstreaming.agents for date 2025-06-23...
2025-07-03 16:12:10 - devo_backup - INFO - Executing Devo query: from edr.crowdstrike.falconstreaming.agents select count() as record_count...
2025-07-03 16:12:10 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:12:10 - devo_backup - INFO -   2025-06-23: 0 records
2025-07-03 16:12:10 - devo_backup - INFO - Testing edr.crowdstrike.falconstreaming.agents for date 2025-06-22...
2025-07-03 16:12:10 - devo_backup - INFO - Executing Devo query: from edr.crowdstrike.falconstreaming.agents select count() as record_count...
2025-07-03 16:12:10 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:12:10 - devo_backup - INFO -   2025-06-22: 0 records
2025-07-03 16:12:10 - devo_backup - INFO - Testing edr.crowdstrike.falconstreaming.agents for date 2025-06-21...
2025-07-03 16:12:10 - devo_backup - INFO - Executing Devo query: from edr.crowdstrike.falconstreaming.agents select count() as record_count...
2025-07-03 16:12:10 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:12:10 - devo_backup - INFO -   2025-06-21: 0 records
2025-07-03 16:12:10 - devo_backup - INFO - Testing edr.crowdstrike.falconstreaming.agents for date 2025-06-20...
2025-07-03 16:12:10 - devo_backup - INFO - Executing Devo query: from edr.crowdstrike.falconstreaming.agents select count() as record_count...
2025-07-03 16:12:10 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:12:10 - devo_backup - INFO -   2025-06-20: 0 records
2025-07-03 16:12:10 - devo_backup - INFO - Devo SDK client initialized with endpoint: https://api-apac.devo.com/search/query
2025-07-03 16:12:10 - devo_backup - INFO - Testing table 'firewall.fortinet.traffic.forward' across 14 dates...
2025-07-03 16:12:10 - devo_backup - INFO - Testing firewall.fortinet.traffic.forward for date 2025-07-03...
2025-07-03 16:12:10 - devo_backup - INFO - Executing Devo query: from firewall.fortinet.traffic.forward select count() as record_count...
2025-07-03 16:12:10 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:12:10 - devo_backup - INFO -   2025-07-03: 0 records
2025-07-03 16:12:10 - devo_backup - INFO - Testing firewall.fortinet.traffic.forward for date 2025-07-02...
2025-07-03 16:12:10 - devo_backup - INFO - Executing Devo query: from firewall.fortinet.traffic.forward select count() as record_count...
2025-07-03 16:12:10 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:12:10 - devo_backup - INFO -   2025-07-02: 0 records
2025-07-03 16:12:10 - devo_backup - INFO - Testing firewall.fortinet.traffic.forward for date 2025-07-01...
2025-07-03 16:12:10 - devo_backup - INFO - Executing Devo query: from firewall.fortinet.traffic.forward select count() as record_count...
2025-07-03 16:12:11 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:12:11 - devo_backup - INFO -   2025-07-01: 0 records
2025-07-03 16:12:11 - devo_backup - INFO - Testing firewall.fortinet.traffic.forward for date 2025-06-30...
2025-07-03 16:12:11 - devo_backup - INFO - Executing Devo query: from firewall.fortinet.traffic.forward select count() as record_count...
2025-07-03 16:12:11 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:12:11 - devo_backup - INFO -   2025-06-30: 0 records
2025-07-03 16:12:11 - devo_backup - INFO - Testing firewall.fortinet.traffic.forward for date 2025-06-29...
2025-07-03 16:12:11 - devo_backup - INFO - Executing Devo query: from firewall.fortinet.traffic.forward select count() as record_count...
2025-07-03 16:12:11 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:12:11 - devo_backup - INFO -   2025-06-29: 0 records
2025-07-03 16:12:11 - devo_backup - INFO - Testing firewall.fortinet.traffic.forward for date 2025-06-28...
2025-07-03 16:12:11 - devo_backup - INFO - Executing Devo query: from firewall.fortinet.traffic.forward select count() as record_count...
2025-07-03 16:12:11 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:12:11 - devo_backup - INFO -   2025-06-28: 0 records
2025-07-03 16:12:11 - devo_backup - INFO - Testing firewall.fortinet.traffic.forward for date 2025-06-27...
2025-07-03 16:12:11 - devo_backup - INFO - Executing Devo query: from firewall.fortinet.traffic.forward select count() as record_count...
2025-07-03 16:12:11 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:12:11 - devo_backup - INFO -   2025-06-27: 0 records
2025-07-03 16:12:11 - devo_backup - INFO - Testing firewall.fortinet.traffic.forward for date 2025-06-26...
2025-07-03 16:12:11 - devo_backup - INFO - Executing Devo query: from firewall.fortinet.traffic.forward select count() as record_count...
2025-07-03 16:12:11 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:12:11 - devo_backup - INFO -   2025-06-26: 0 records
2025-07-03 16:12:11 - devo_backup - INFO - Testing firewall.fortinet.traffic.forward for date 2025-06-25...
2025-07-03 16:12:11 - devo_backup - INFO - Executing Devo query: from firewall.fortinet.traffic.forward select count() as record_count...
2025-07-03 16:12:11 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:12:11 - devo_backup - INFO -   2025-06-25: 0 records
2025-07-03 16:12:11 - devo_backup - INFO - Testing firewall.fortinet.traffic.forward for date 2025-06-24...
2025-07-03 16:12:11 - devo_backup - INFO - Executing Devo query: from firewall.fortinet.traffic.forward select count() as record_count...
2025-07-03 16:12:11 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:12:11 - devo_backup - INFO -   2025-06-24: 0 records
2025-07-03 16:12:11 - devo_backup - INFO - Testing firewall.fortinet.traffic.forward for date 2025-06-23...
2025-07-03 16:12:11 - devo_backup - INFO - Executing Devo query: from firewall.fortinet.traffic.forward select count() as record_count...
2025-07-03 16:12:12 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:12:12 - devo_backup - INFO -   2025-06-23: 0 records
2025-07-03 16:12:12 - devo_backup - INFO - Testing firewall.fortinet.traffic.forward for date 2025-06-22...
2025-07-03 16:12:12 - devo_backup - INFO - Executing Devo query: from firewall.fortinet.traffic.forward select count() as record_count...
2025-07-03 16:12:12 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:12:12 - devo_backup - INFO -   2025-06-22: 0 records
2025-07-03 16:12:12 - devo_backup - INFO - Testing firewall.fortinet.traffic.forward for date 2025-06-21...
2025-07-03 16:12:12 - devo_backup - INFO - Executing Devo query: from firewall.fortinet.traffic.forward select count() as record_count...
2025-07-03 16:12:12 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:12:12 - devo_backup - INFO -   2025-06-21: 0 records
2025-07-03 16:12:12 - devo_backup - INFO - Testing firewall.fortinet.traffic.forward for date 2025-06-20...
2025-07-03 16:12:12 - devo_backup - INFO - Executing Devo query: from firewall.fortinet.traffic.forward select count() as record_count...
2025-07-03 16:12:12 - devo_backup - INFO - Query returned 0 records
2025-07-03 16:12:12 - devo_backup - INFO -   2025-06-20: 0 records
2025-07-03 16:12:12 - devo_backup - INFO - Devo SDK client initialized with endpoint: https://api-apac.devo.com/search/query
2025-07-03 16:12:12 - devo_backup - INFO - Testing table 'siem.logtrust.web.activity' across 14 dates...
2025-07-03 16:12:12 - devo_backup - INFO - Testing siem.logtrust.web.activity for date 2025-07-03...
2025-07-03 16:12:12 - devo_backup - INFO - Executing Devo query: from siem.logtrust.web.activity select count() as record_count...
