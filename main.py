#!/usr/bin/env python3
"""
Devo API Data Backup System - Main Script

This script pulls data from Devo API with three execution modes:
1. Default mode: Pull data for current day
2. Specific date mode: Pull data for a specific date
3. Date range mode: Pull data for a date range

Usage:
    python main.py                           # Current day
    python main.py 2024-01-15               # Specific date
    python main.py 2024-01-15 2024-01-20    # Date range
"""

import sys
import json
import os
from datetime import datetime
from typing import Dict, List, Any

from config import config
from logger import logger
from date_utils import DateUtils
from devo_client import DevoClient, DevoAPIError
from arg_parser import parse_args, ExecutionMode, ArgumentParser


class BackupOrchestrator:
    """Main orchestrator for the backup system."""
    
    def __init__(self):
        """Initialize the backup orchestrator."""
        self.devo_client = DevoClient()
        self.results = {}
        self.stats = {
            'total_dates': 0,
            'successful_dates': 0,
            'failed_dates': 0,
            'total_records': 0,
            'start_time': None,
            'end_time': None
        }
    
    def run_backup(self, dates: List[str]) -> bool:
        """
        Run backup for the specified dates.
        
        Args:
            dates: List of dates in YYYY-MM-DD format
            
        Returns:
            True if backup completed successfully, False otherwise
        """
        self.stats['start_time'] = datetime.now()
        self.stats['total_dates'] = len(dates)
        
        logger.info(f"Starting backup for {len(dates)} date(s)")
        
        # Test connection first
        if not self._test_connection():
            logger.error("Connection test failed. Aborting backup.")
            return False
        
        # Process each date
        for i, date_str in enumerate(dates, 1):
            logger.info(f"Processing date {i}/{len(dates)}: {date_str}")
            
            try:
                date_results = self._backup_date(date_str)
                self.results[date_str] = date_results
                
                # Update statistics
                if self._is_date_successful(date_results):
                    self.stats['successful_dates'] += 1
                    self.stats['total_records'] += self._count_records(date_results)
                else:
                    self.stats['failed_dates'] += 1
                
                logger.info(f"Completed date {date_str}")
                
            except Exception as e:
                logger.exception(f"Unexpected error processing date {date_str}: {str(e)}")
                self.results[date_str] = {
                    'success': False,
                    'error': str(e),
                    'tables': {}
                }
                self.stats['failed_dates'] += 1
        
        self.stats['end_time'] = datetime.now()
        
        # Generate summary
        self._generate_summary()
        
        # Save results
        self._save_results()
        
        return self.stats['failed_dates'] == 0
    
    def _test_connection(self) -> bool:
        """Test connection to Devo API."""
        logger.info("Testing connection to Devo API...")
        return self.devo_client.test_connection()
    
    def _backup_date(self, date_str: str) -> Dict[str, Any]:
        """
        Backup data for a specific date.
        
        Args:
            date_str: Date in YYYY-MM-DD format
            
        Returns:
            Dictionary with backup results
        """
        try:
            # Pull data from Devo
            table_results = self.devo_client.pull_data_for_date(date_str)
            
            # Process results
            successful_tables = 0
            failed_tables = 0
            total_records = 0
            
            for table_name, table_result in table_results.items():
                if table_result['success']:
                    successful_tables += 1
                    total_records += table_result['record_count']
                else:
                    failed_tables += 1
            
            logger.log_data_pull(date_str, total_records, failed_tables == 0)
            
            return {
                'success': failed_tables == 0,
                'date': date_str,
                'tables': table_results,
                'summary': {
                    'total_tables': len(table_results),
                    'successful_tables': successful_tables,
                    'failed_tables': failed_tables,
                    'total_records': total_records
                }
            }
            
        except DevoAPIError as e:
            logger.error(f"Devo API error for date {date_str}: {str(e)}")
            return {
                'success': False,
                'date': date_str,
                'error': str(e),
                'tables': {}
            }
        except Exception as e:
            logger.exception(f"Unexpected error for date {date_str}: {str(e)}")
            return {
                'success': False,
                'date': date_str,
                'error': str(e),
                'tables': {}
            }
    
    def _is_date_successful(self, date_result: Dict[str, Any]) -> bool:
        """Check if a date backup was successful."""
        return date_result.get('success', False)
    
    def _count_records(self, date_result: Dict[str, Any]) -> int:
        """Count total records for a date."""
        if 'summary' in date_result:
            return date_result['summary'].get('total_records', 0)
        return 0
    
    def _generate_summary(self):
        """Generate and log backup summary."""
        duration = self.stats['end_time'] - self.stats['start_time']
        
        logger.info("=" * 60)
        logger.info("BACKUP SUMMARY")
        logger.info("=" * 60)
        logger.info(f"Total dates processed: {self.stats['total_dates']}")
        logger.info(f"Successful dates: {self.stats['successful_dates']}")
        logger.info(f"Failed dates: {self.stats['failed_dates']}")
        logger.info(f"Total records pulled: {self.stats['total_records']:,}")
        logger.info(f"Duration: {duration}")
        logger.info(f"Start time: {self.stats['start_time']}")
        logger.info(f"End time: {self.stats['end_time']}")
        
        if self.stats['failed_dates'] > 0:
            logger.warning(f"Backup completed with {self.stats['failed_dates']} failed dates")
        else:
            logger.info("Backup completed successfully!")
        
        logger.log_backup_summary(
            self.stats['total_dates'],
            self.stats['successful_dates'],
            self.stats['failed_dates']
        )
    
    def _save_results(self):
        """Save backup results to file."""
        try:
            # Create results directory
            os.makedirs("results", exist_ok=True)
            
            # Generate filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"results/backup_results_{timestamp}.json"
            
            # Prepare results data
            results_data = {
                'metadata': {
                    'timestamp': timestamp,
                    'start_time': self.stats['start_time'].isoformat(),
                    'end_time': self.stats['end_time'].isoformat(),
                    'duration_seconds': (self.stats['end_time'] - self.stats['start_time']).total_seconds(),
                    'statistics': self.stats
                },
                'results': self.results
            }
            
            # Save to file
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(results_data, f, indent=2, ensure_ascii=False, default=str)
            
            logger.info(f"Results saved to: {filename}")
            
        except Exception as e:
            logger.error(f"Failed to save results: {str(e)}")


def main():
    """Main entry point."""
    try:
        # Parse command-line arguments
        mode, dates = parse_args()
        
        # Log execution plan
        parser = ArgumentParser()
        summary = parser.get_execution_summary(mode, dates)
        logger.info(summary)
        
        # Initialize and run backup
        orchestrator = BackupOrchestrator()
        success = orchestrator.run_backup(dates)
        
        # Exit with appropriate code
        if success:
            logger.info("Backup system completed successfully")
            sys.exit(0)
        else:
            logger.error("Backup system completed with errors")
            sys.exit(1)
            
    except KeyboardInterrupt:
        logger.warning("Backup interrupted by user")
        sys.exit(130)
    except Exception as e:
        logger.exception(f"Unexpected error in main: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
