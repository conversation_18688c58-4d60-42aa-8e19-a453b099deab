#!/usr/bin/env python3
"""
Devo Data Volume Analysis Script

This script performs comprehensive data volume analysis on discovered tables.
It queries each table for a specific date to determine actual record counts
and data distribution across the Devo environment.

Usage:
    python data_volume_analysis.py                           # Use July 1, 2025
    python data_volume_analysis.py --date 2025-06-30         # Custom date
    python data_volume_analysis.py --input discovery.json    # Custom discovery file
    python data_volume_analysis.py --verbose                 # Verbose logging
"""

import sys
import json
import os
import argparse
import logging
from datetime import datetime, date
from typing import Dict, List, Any, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed
import time

from config import config
from logger import logger
from date_utils import DateUtils
from devo_client import DevoClient, DevoAPIError


class DataVolumeAnalysisError(Exception):
    """Custom exception for data volume analysis errors."""
    pass


class DataVolumeAnalyzer:
    """Comprehensive data volume analyzer for Devo tables."""

    def __init__(self, target_date: str = "2025-07-01"):
        """
        Initialize the data volume analyzer.
        
        Args:
            target_date: Date to analyze in YYYY-MM-DD format
        """
        self.devo_client = DevoClient()
        self.target_date = target_date
        self.analysis_results = {}
        self.stats = {
            'start_time': datetime.now(),
            'end_time': None,
            'target_date': target_date,
            'total_tables_analyzed': 0,
            'successful_queries': 0,
            'failed_queries': 0,
            'tables_with_data': 0,
            'tables_without_data': 0,
            'total_records_found': 0,
            'analysis_errors': []
        }
        
    def load_discovered_tables(self, input_file: str = None) -> List[str]:
        """
        Load table list from discovery results.
        
        Args:
            input_file: Path to discovery results file (if None, uses latest)
            
        Returns:
            List of table names to analyze
        """
        if input_file is None:
            # Find the latest discovery results file
            results_dir = "results"
            if not os.path.exists(results_dir):
                raise DataVolumeAnalysisError("Results directory not found. Run table discovery first.")
            
            discovery_files = [
                f for f in os.listdir(results_dir) 
                if f.startswith("table_discovery_results_") and f.endswith(".json")
            ]
            
            if not discovery_files:
                raise DataVolumeAnalysisError("No table discovery results found. Run table discovery first.")
            
            # Sort by filename (which includes timestamp) to get the latest
            discovery_files.sort(reverse=True)
            input_file = os.path.join(results_dir, discovery_files[0])
            logger.info(f"Using latest discovery results: {input_file}")
        
        try:
            with open(input_file, 'r', encoding='utf-8') as f:
                discovery_data = json.load(f)
            
            # Extract table names from discovery results
            tables = list(discovery_data.get('tables', {}).keys())
            logger.info(f"Loaded {len(tables)} tables from discovery results")
            return tables
            
        except Exception as e:
            raise DataVolumeAnalysisError(f"Failed to load discovery results from {input_file}: {str(e)}")
    
    def analyze_data_volumes(self, tables: List[str]) -> Dict[str, Any]:
        """
        Analyze data volumes for all tables on the target date.
        
        Args:
            tables: List of table names to analyze
            
        Returns:
            Dictionary containing analysis results
        """
        logger.info(f"Starting data volume analysis for {len(tables)} tables on {self.target_date}")
        
        try:
            # Test connection first
            if not self._test_connection():
                raise DataVolumeAnalysisError("Failed to connect to Devo API")
            
            self.stats['total_tables_analyzed'] = len(tables)
            
            # Analyze tables in parallel for efficiency
            logger.info("Analyzing table data volumes...")
            self._analyze_tables_parallel(tables)
            
            # Generate final results
            return self._generate_final_results()
            
        except Exception as e:
            logger.exception(f"Data volume analysis failed: {str(e)}")
            self.stats['analysis_errors'].append(str(e))
            return self._generate_final_results()
        finally:
            self.stats['end_time'] = datetime.now()
    
    def _test_connection(self) -> bool:
        """Test connection to Devo API."""
        logger.info("Testing connection to Devo API...")
        return self.devo_client.test_connection()
    
    def _analyze_tables_parallel(self, tables: List[str]) -> None:
        """
        Analyze tables in parallel for better performance.
        
        Args:
            tables: List of table names to analyze
        """
        # Use threading for parallel analysis
        max_workers = min(8, len(tables))  # Limit concurrent requests to avoid overwhelming API
        
        logger.info(f"Starting parallel analysis with {max_workers} workers...")
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit analysis tasks
            future_to_table = {
                executor.submit(self._analyze_single_table_volume, table): table 
                for table in tables
            }
            
            # Process completed tasks
            completed = 0
            for future in as_completed(future_to_table):
                table = future_to_table[future]
                completed += 1
                
                try:
                    table_result = future.result()
                    self.analysis_results[table] = table_result
                    
                    if table_result['success']:
                        self.stats['successful_queries'] += 1
                        record_count = table_result.get('record_count', 0)
                        self.stats['total_records_found'] += record_count
                        
                        if record_count > 0:
                            self.stats['tables_with_data'] += 1
                        else:
                            self.stats['tables_without_data'] += 1
                    else:
                        self.stats['failed_queries'] += 1
                    
                    # Progress indicator
                    if completed % 10 == 0 or completed == len(tables):
                        logger.info(f"Progress: {completed}/{len(tables)} tables analyzed")
                        
                except Exception as e:
                    logger.error(f"Failed to analyze table {table}: {str(e)}")
                    self.analysis_results[table] = {
                        'table_name': table,
                        'success': False,
                        'error': str(e),
                        'record_count': 0,
                        'analysis_timestamp': datetime.now().isoformat()
                    }
                    self.stats['failed_queries'] += 1
    
    def _analyze_single_table_volume(self, table_name: str) -> Dict[str, Any]:
        """
        Analyze data volume for a single table on the target date.
        
        Args:
            table_name: Name of the table to analyze
            
        Returns:
            Dictionary with analysis results
        """
        logger.debug(f"Analyzing data volume for table: {table_name}")
        
        result = {
            'table_name': table_name,
            'success': False,
            'record_count': 0,
            'target_date': self.target_date,
            'query_duration_seconds': 0,
            'error': None,
            'analysis_timestamp': datetime.now().isoformat()
        }
        
        try:
            # Create time range for target date
            start_time, end_time = DateUtils.format_date_for_query(self.target_date)
            
            # Execute count query
            start_query_time = time.time()
            count_query = f"from {table_name} select count() as record_count"
            
            logger.debug(f"Executing count query for {table_name}: {count_query}")
            query_result = self.devo_client.query_data(count_query, start_time, end_time)
            
            query_duration = time.time() - start_query_time
            result['query_duration_seconds'] = round(query_duration, 3)
            
            # Process query result
            if query_result and query_result.get('object'):
                records = query_result['object']
                if records and len(records) > 0:
                    # Extract count from first record
                    first_record = records[0]
                    if isinstance(first_record, dict) and 'record_count' in first_record:
                        record_count = first_record['record_count']
                        result['record_count'] = int(record_count) if record_count is not None else 0
                    elif isinstance(first_record, (int, float)):
                        result['record_count'] = int(first_record)
                    else:
                        result['record_count'] = 0
                else:
                    result['record_count'] = 0
            else:
                result['record_count'] = 0
            
            result['success'] = True
            logger.debug(f"Successfully analyzed {table_name}: {result['record_count']} records")
            
        except Exception as e:
            error_msg = str(e)
            logger.debug(f"Failed to analyze table {table_name}: {error_msg}")
            result['error'] = error_msg
            result['success'] = False
        
        return result
    
    def _generate_final_results(self) -> Dict[str, Any]:
        """Generate final analysis results."""
        duration = (self.stats['end_time'] - self.stats['start_time']).total_seconds() if self.stats['end_time'] else 0
        
        results = {
            'analysis_metadata': {
                'timestamp': datetime.now().isoformat(),
                'analysis_duration_seconds': duration,
                'target_date': self.target_date,
                'devo_endpoint': config.devo_query_endpoint,
                'analysis_version': '1.0.0'
            },
            'statistics': {
                'total_tables_analyzed': self.stats['total_tables_analyzed'],
                'successful_queries': self.stats['successful_queries'],
                'failed_queries': self.stats['failed_queries'],
                'tables_with_data': self.stats['tables_with_data'],
                'tables_without_data': self.stats['tables_without_data'],
                'total_records_found': self.stats['total_records_found'],
                'analysis_errors': self.stats['analysis_errors']
            },
            'table_volumes': self.analysis_results
        }
        
        return results

    def save_results(self, results: Dict[str, Any], output_file: Optional[str] = None) -> str:
        """
        Save analysis results to JSON file.

        Args:
            results: Analysis results dictionary
            output_file: Optional custom output filename

        Returns:
            Path to saved file
        """
        if output_file is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            target_date_str = self.target_date.replace('-', '')
            output_file = f"data_volume_analysis_{target_date_str}_{timestamp}.json"

        # Ensure results directory exists
        results_dir = "results"
        if not os.path.exists(results_dir):
            os.makedirs(results_dir)

        output_path = os.path.join(results_dir, output_file)

        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False, default=str)

            logger.info(f"Analysis results saved to: {output_path}")
            return output_path

        except Exception as e:
            logger.error(f"Failed to save results to {output_path}: {str(e)}")
            raise DataVolumeAnalysisError(f"Failed to save results: {str(e)}")

    def print_summary(self, results: Dict[str, Any]) -> None:
        """Print a comprehensive summary of analysis results."""
        stats = results['statistics']
        metadata = results['analysis_metadata']

        print("\n" + "="*80)
        print("DEVO DATA VOLUME ANALYSIS SUMMARY")
        print("="*80)
        print(f"Analysis completed: {metadata['timestamp']}")
        print(f"Target date analyzed: {metadata['target_date']}")
        print(f"Analysis duration: {metadata['analysis_duration_seconds']:.2f} seconds")
        print(f"Devo endpoint: {metadata['devo_endpoint']}")
        print()

        # Overall statistics
        print("OVERALL STATISTICS:")
        print(f"  Total tables analyzed: {stats['total_tables_analyzed']}")
        print(f"  Successful queries: {stats['successful_queries']}")
        print(f"  Failed queries: {stats['failed_queries']}")
        print(f"  Tables with data: {stats['tables_with_data']}")
        print(f"  Tables without data: {stats['tables_without_data']}")
        print(f"  Total records found: {stats['total_records_found']:,}")
        print()

        # Show analysis errors if any
        if stats['analysis_errors']:
            print("ANALYSIS ERRORS:")
            for error in stats['analysis_errors']:
                print(f"  - {error}")
            print()

        # Tables with data (sorted by record count)
        tables_with_data = {
            name: info for name, info in results['table_volumes'].items()
            if info.get('success', False) and info.get('record_count', 0) > 0
        }

        if tables_with_data:
            print("TABLES WITH DATA (sorted by record count):")
            sorted_tables = sorted(
                tables_with_data.items(),
                key=lambda x: x[1].get('record_count', 0),
                reverse=True
            )

            for i, (table_name, table_info) in enumerate(sorted_tables, 1):
                record_count = table_info.get('record_count', 0)
                query_duration = table_info.get('query_duration_seconds', 0)
                print(f"  {i:2d}. {table_name}")
                print(f"      Records: {record_count:,} | Query time: {query_duration:.3f}s")
        else:
            print("NO TABLES WITH DATA FOUND")
            print("  All tables returned 0 records for the target date.")

        # Tables without data (show first 10)
        tables_without_data = [
            name for name, info in results['table_volumes'].items()
            if info.get('success', False) and info.get('record_count', 0) == 0
        ]

        if tables_without_data:
            print(f"\nTABLES WITHOUT DATA ({len(tables_without_data)} total):")
            for i, table_name in enumerate(tables_without_data[:10], 1):
                print(f"  {i:2d}. {table_name}")
            if len(tables_without_data) > 10:
                print(f"  ... and {len(tables_without_data) - 10} more tables")

        # Failed queries
        failed_tables = [
            name for name, info in results['table_volumes'].items()
            if not info.get('success', False)
        ]

        if failed_tables:
            print(f"\nFAILED QUERIES ({len(failed_tables)} total):")
            for i, table_name in enumerate(failed_tables[:5], 1):
                table_info = results['table_volumes'][table_name]
                error = table_info.get('error', 'Unknown error')
                print(f"  {i:2d}. {table_name}")
                print(f"      Error: {error}")
            if len(failed_tables) > 5:
                print(f"  ... and {len(failed_tables) - 5} more failed queries")

        print("="*80)


def parse_arguments() -> argparse.Namespace:
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Analyze data volumes in discovered Devo tables",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python data_volume_analysis.py                           # Analyze July 1, 2025
  python data_volume_analysis.py --date 2025-06-30         # Custom date
  python data_volume_analysis.py --input discovery.json    # Custom discovery file
  python data_volume_analysis.py --verbose                 # Verbose logging
        """
    )

    parser.add_argument(
        '--date', '-d',
        type=str,
        default='2025-07-01',
        help='Target date to analyze in YYYY-MM-DD format (default: 2025-07-01)'
    )

    parser.add_argument(
        '--input', '-i',
        type=str,
        help='Input discovery results file (default: latest in results/)'
    )

    parser.add_argument(
        '--output', '-o',
        type=str,
        help='Output filename for results (default: auto-generated with timestamp)'
    )

    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Enable verbose logging'
    )

    return parser.parse_args()


def validate_date(date_string: str) -> bool:
    """Validate date format."""
    try:
        datetime.strptime(date_string, '%Y-%m-%d')
        return True
    except ValueError:
        return False


def main():
    """Main entry point."""
    try:
        # Parse arguments
        args = parse_arguments()

        # Validate date format
        if not validate_date(args.date):
            print(f"Error: Invalid date format '{args.date}'. Use YYYY-MM-DD format.")
            sys.exit(1)

        # Set logging level
        if args.verbose:
            logger.logger.setLevel(logging.DEBUG)
            # Also set console handler to DEBUG
            for handler in logger.logger.handlers:
                if isinstance(handler, logging.StreamHandler) and not isinstance(handler, logging.FileHandler):
                    handler.setLevel(logging.DEBUG)

        logger.info("Starting Devo Data Volume Analysis...")
        logger.info(f"Target date: {args.date}")
        logger.info(f"Devo endpoint: {config.devo_query_endpoint}")

        # Initialize analyzer
        analyzer = DataVolumeAnalyzer(target_date=args.date)

        # Load discovered tables
        logger.info("Loading discovered tables...")
        tables = analyzer.load_discovered_tables(args.input)

        if not tables:
            logger.error("No tables found to analyze")
            sys.exit(1)

        # Run analysis
        results = analyzer.analyze_data_volumes(tables)

        # Save results
        output_file = analyzer.save_results(results, args.output)

        # Print summary
        analyzer.print_summary(results)

        # Final status
        stats = results['statistics']
        if stats['failed_queries'] == 0:
            logger.info("Data volume analysis completed successfully!")
            print(f"\nResults saved to: {output_file}")
            sys.exit(0)
        else:
            logger.warning(f"Data volume analysis completed with {stats['failed_queries']} failures")
            print(f"\nResults saved to: {output_file}")
            sys.exit(1)

    except KeyboardInterrupt:
        logger.warning("Data volume analysis interrupted by user")
        sys.exit(130)
    except Exception as e:
        logger.exception(f"Data volume analysis failed: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
